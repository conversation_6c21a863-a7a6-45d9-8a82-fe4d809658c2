from datetime import datetime
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from enum import Enum
import os
import uuid
import logging
from passlib.context import CryptContext
from database import db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 密码哈希工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 用户角色枚举
class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"

# 用户数据模型
class User(BaseModel):
    id: str
    username: str
    email: EmailStr
    hashed_password: str
    role: UserRole = UserRole.USER
    created_at: datetime = Field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    is_active: bool = True
    points: int = 0  # 用户积分
    expiry_date: Optional[datetime] = None  # 账户过期时间

    def verify_password(self, plain_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, self.hashed_password)
    
    @classmethod
    def from_db_row(cls, row):
        """从数据库行创建用户对象"""
        if not row:
            return None
            
        user_dict = dict(row)
        
        # 处理布尔值转换
        user_dict["is_active"] = bool(user_dict["is_active"])
        
        # 处理日期时间转换
        user_dict["created_at"] = datetime.fromisoformat(user_dict["created_at"])
        if user_dict["last_login"]:
            user_dict["last_login"] = datetime.fromisoformat(user_dict["last_login"])
            
        # 处理过期时间
        if "expiry_date" in user_dict and user_dict["expiry_date"]:
            user_dict["expiry_date"] = datetime.fromisoformat(user_dict["expiry_date"])
            
        # 处理积分（确保是整数）
        if "points" in user_dict:
            user_dict["points"] = int(user_dict["points"]) if user_dict["points"] is not None else 0
        else:
            user_dict["points"] = 0
        
        return cls(**user_dict)

# 用户信息模型(不包含密码)
class UserInfo(BaseModel):
    id: str
    username: str
    email: EmailStr
    role: UserRole
    created_at: datetime
    last_login: Optional[datetime]
    is_active: bool

# 用户数据库管理类
class UserDB:
    def __init__(self):
        pass
            
    def get_user_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        try:
            row = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
            return User.from_db_row(row)
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
        
    def get_user_by_email(self, email: str) -> Optional[User]:
        """通过邮箱获取用户"""
        try:
            row = db.fetch_one("SELECT * FROM users WHERE email = ?", (email,))
            return User.from_db_row(row)
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
        
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """通过ID获取用户"""
        try:
            row = db.fetch_one("SELECT * FROM users WHERE id = ?", (user_id,))
            return User.from_db_row(row)
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def create_user(self, username: str, email: str, password: str, role: UserRole = UserRole.USER) -> User:
        """创建新用户"""
        try:
            # 检查用户名或邮箱是否已存在
            if self.get_user_by_username(username):
                raise ValueError("用户名已存在")
            if self.get_user_by_email(email):
                raise ValueError("邮箱已存在")
                
            # 生成用户ID
            user_id = str(uuid.uuid4())
            
            # 哈希密码
            hashed_password = pwd_context.hash(password)
            
            # 创建用户数据
            now = datetime.now().isoformat()
            user_data = {
                "id": user_id,
                "username": username,
                "email": email,
                "hashed_password": hashed_password,
                "role": role,
                "created_at": now,
                "last_login": None,
                "is_active": 1,  # SQLite中布尔值用0/1表示
                "points": 0,
                "expiry_date": None
            }
            
            # 插入数据库
            db.insert("users", user_data)
            
            # 返回用户对象
            return User(
                id=user_id,
                username=username,
                email=email,
                hashed_password=hashed_password,
                role=role,
                created_at=datetime.fromisoformat(now),
                is_active=True,
                points=0,
                expiry_date=None
            )
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            raise
        
    def update_user(self, user: User) -> bool:
        """更新用户信息"""
        try:
            # 准备更新数据
            update_data = {
                "username": user.username,
                "email": user.email,
                "hashed_password": user.hashed_password,
                "role": user.role,
                "is_active": 1 if user.is_active else 0,
                "points": user.points,
                "expiry_date": user.expiry_date.isoformat() if user.expiry_date else None
            }
            
            # 如果有最后登录时间，则更新
            if user.last_login:
                update_data["last_login"] = user.last_login.isoformat()
            
            # 更新条件
            condition = {"id": user.id}
            
            # 执行更新
            rows_affected = db.update("users", update_data, condition)
            return rows_affected > 0
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return False
        
    def update_last_login(self, username: str) -> bool:
        """更新用户最后登录时间"""
        try:
            user = self.get_user_by_username(username)
            if user:
                user.last_login = datetime.now()
                return self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"更新登录时间失败: {e}")
            return False
        
    def list_users(self) -> List[User]:
        """获取所有用户列表"""
        try:
            rows = db.fetch_all("SELECT * FROM users")
            return [User.from_db_row(row) for row in rows]
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
        
    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            condition = {"id": user_id}
            rows_affected = db.delete("users", condition)
            return rows_affected > 0
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return False

    def create_admin_if_not_exists(self):
        """如果不存在管理员账户，则创建一个默认管理员账户"""
        try:
            # 检查是否已有管理员账户
            admin_exists = db.fetch_one("SELECT 1 FROM users WHERE role = ?", (UserRole.ADMIN,))
            if admin_exists:
                return  # 已有管理员，不需要创建

            # 创建指定管理员账户
            try:
                self.create_user(
                    username="wlg_admin",
                    email="<EMAIL>",
                    password="wlg_admin123",  # 管理员密码
                    role=UserRole.ADMIN
                )
                logger.info("已创建管理员账户 wlg_admin")
            except ValueError as e:
                logger.warning(f"创建管理员账户失败: {e}")
        except Exception as e:
            logger.error(f"检查管理员账户失败: {e}") 