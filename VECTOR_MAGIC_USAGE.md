# Vector Magic 使用指南

## 快速开始

### 1. 管理员配置Vector Magic路径

1. **启动应用程序**
   ```bash
   python app.py
   ```

2. **访问管理员界面**
   - 打开浏览器访问: http://127.0.0.1:8001/admin
   - 使用管理员账户登录

3. **配置Vector Magic路径**
   - 点击左侧菜单的"矢量图配置"
   - 在"路径配置"部分输入Vector Magic可执行文件的完整路径
   - 常见路径：`C:\Program Files (x86)\Vector Magic\vmde.exe`
   - 点击"设置路径"按钮保存配置
   - 点击"检查状态"验证配置是否成功

### 2. 使用矢量图生成功能

1. **访问矢量图页面**
   - 登录后访问: http://127.0.0.1:8001/vectorize

2. **上传图片**
   - 点击"选择文件"按钮选择要转换的图片
   - 支持格式：JPG, PNG, GIF, BMP

3. **选择质量级别**
   - **低质量**: 快速处理，适合简单图像
   - **中等质量**: 平衡质量和速度（推荐）
   - **高质量**: 最佳质量，处理时间较长

4. **开始转换**
   - 点击"生成矢量图"按钮
   - 系统将在后台处理，请耐心等待

5. **下载结果**
   - 处理完成后，可以下载以下文件：
     - SVG文件：矢量图格式
     - PNG文件：高质量位图
     - 预览图：处理结果预览

## 故障排除

### 常见错误及解决方案

1. **"Vector Magic未安装或路径不正确"**
   - 检查Vector Magic是否已正确安装
   - 确认配置的路径指向正确的可执行文件（通常是vmde.exe）
   - 使用管理员界面重新设置路径

2. **"系统缺少必要的win32模块"**
   - 安装pywin32模块：
     ```bash
     pip install pywin32
     ```

3. **"无法找到Vector Magic窗口"**
   - 确保Vector Magic能够正常启动
   - 检查是否有防病毒软件阻止程序运行
   - 尝试手动启动Vector Magic测试

4. **处理超时或失败**
   - 检查图片文件是否损坏
   - 尝试使用较小的图片文件
   - 确保系统有足够的内存和磁盘空间

### 使用测试工具

运行配置测试脚本：
```bash
python test_vector_magic.py
```

交互式配置：
```bash
python test_vector_magic.py --setup
```

Windows快速配置：
```bash
setup_vector_magic.bat
```

## 技术说明

### 自动化工作流程

系统使用以下步骤自动处理图片：

1. **启动Vector Magic**: 使用配置的路径启动程序
2. **窗口检测**: 智能等待并检测Vector Magic窗口
3. **界面交互**: 根据质量级别执行相应的操作序列
4. **处理等待**: 等待Vector Magic完成图片处理
5. **结果保存**: 自动保存SVG和PNG格式的结果
6. **资源清理**: 关闭程序并清理临时文件

### 质量级别说明

- **低质量 (Low)**: 使用自动模式，快速处理
- **中等质量 (Medium)**: 使用默认设置，平衡质量和速度
- **高质量 (High)**: 使用高级设置，获得最佳质量

### 支持的文件格式

**输入格式:**
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- TIFF (.tiff, .tif)

**输出格式:**
- SVG (矢量图)
- PNG (高质量位图)

## 系统要求

### 软件要求
- Windows 7/8/10/11
- Python 3.7+
- Vector Magic Desktop Edition
- 必需的Python包：
  - pywin32
  - pyautogui
  - fastapi
  - uvicorn

### 硬件要求
- 内存：至少2GB可用内存
- 磁盘空间：至少1GB可用空间
- 显示器：支持1024x768分辨率

## 安全注意事项

1. **管理员权限**: 只有管理员可以配置Vector Magic路径
2. **文件安全**: 系统会验证上传文件的类型和大小
3. **路径验证**: 系统会检查配置路径的有效性
4. **进程隔离**: 每次处理都在独立的进程中运行

## 性能优化建议

1. **图片大小**: 建议上传图片大小不超过10MB
2. **并发处理**: 避免同时处理多个大图片
3. **系统资源**: 确保系统有足够的内存和CPU资源
4. **定期清理**: 定期清理输出目录中的旧文件

## API接口

### 管理员API

- `GET /api/admin/vector-magic/status` - 获取Vector Magic状态
- `POST /api/admin/vector-magic/set-path` - 设置Vector Magic路径
- `GET /api/admin/vector-magic/system-status` - 获取完整系统状态

### 用户API

- `POST /api/vectorize` - 提交矢量图生成请求
- `GET /api/download/vector/{filename}` - 下载结果文件

## 许可证

请确保您拥有Vector Magic的有效许可证。本系统仅提供自动化接口，不包含Vector Magic软件本身。

## 技术支持

如果遇到问题：

1. 首先运行测试脚本诊断问题
2. 检查系统日志获取详细错误信息
3. 确认Vector Magic软件本身工作正常
4. 查看本文档的故障排除部分
5. 联系系统管理员获取帮助

## 更新日志

### v2.0 (当前版本)
- ✅ 支持动态路径配置
- ✅ 添加管理员配置界面
- ✅ 改进自动化执行流程
- ✅ 增强错误处理和日志记录
- ✅ 添加系统状态检查功能
- ✅ 提供配置测试工具

### v1.0 (原始版本)
- 基本的矢量图生成功能
- 硬编码路径配置
- 简单的自动化流程
