@echo off
chcp 65001 >nul
echo ================================================
echo Vector Magic 配置工具
echo ================================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已正确安装并添加到PATH
    pause
    exit /b 1
)

REM 激活虚拟环境（如果存在）
if exist "venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
)

echo 运行Vector Magic配置测试...
echo.
python test_vector_magic.py --setup

echo.
echo 配置完成！
echo 您现在可以：
echo 1. 启动应用程序: python app.py
echo 2. 访问管理界面: http://127.0.0.1:8001/admin
echo 3. 测试矢量图功能: http://127.0.0.1:8001/vectorize
echo.
pause
