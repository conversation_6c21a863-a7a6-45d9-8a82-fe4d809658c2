<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>矢量图生成 - 地毯图案提取系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        .dragover {
            border-color: #0d6efd;
            background-color: #e9f2ff;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">地毯图案提取系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/vectorize">矢量图生成</a>
                    </li>
                    <!-- 管理员显示管理入口 -->
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/admin">管理面板</a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/user-management">用户管理</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">欢迎，<span id="username-display">{{ user.username }}</span> <span class="badge bg-light text-dark">积分: <span id="user-points">{{ user.points }}</span></span></span>
                    <a href="/logout" class="btn btn-outline-light" id="logout-btn">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <header class="my-4 text-center">
            <h1>矢量图生成</h1>
            <p class="lead">上传图片，自动生成高质量矢量图</p>
        </header>
        
        <!-- 显示服务状态警告 -->
        {% if not vector_enabled %}
        <div class="row">
            <div class="col-lg-8 mx-auto mb-4">
                <div class="alert alert-danger">
                    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 服务不可用</h4>
                    <p>矢量图生成服务当前不可用。系统无法加载必要的服务组件。请联系系统管理员安装所需的依赖。</p>
                    <hr>
                    <p class="mb-0">技术信息：需要安装 pywin32 和 pyautogui 包以启用此功能。</p>
                </div>
            </div>
        </div>
        {% elif not has_win32 %}
        <div class="row">
            <div class="col-lg-8 mx-auto mb-4">
                <div class="alert alert-warning">
                    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 部分功能受限</h4>
                    <p>系统缺少必要的win32模块，无法完成矢量图转换。请联系系统管理员安装所需的依赖。</p>
                    <hr>
                    <p class="mb-0">技术信息：需要正确安装 pywin32 包以启用此功能，安装命令：<code>pip install pywin32==303</code></p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">上传图片</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="vector-upload-area">
                            <div class="upload-prompt" id="vector-upload-prompt">
                                <i class="fa fa-cloud-upload fa-3x mb-3"></i>
                                <h4>拖放图片到此处</h4>
                                <p>或</p>
                                <label for="vector-file-input" class="btn btn-outline-primary">选择图片</label>
                                <input id="vector-file-input" type="file" accept="image/jpeg,image/png,image/gif" style="display: none">
                            </div>
                            <div class="preview-area" id="vector-preview-area" style="display: none;">
                                <img id="vector-preview-image" src="#" alt="预览图">
                                <div class="preview-controls mt-3">
                                    <button class="btn btn-sm btn-outline-danger" id="vector-remove-btn">移除</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-group mb-3">
                                <label class="form-label">选择矢量质量：</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="vector-quality" id="quality-low" autocomplete="off" value="low">
                                    <label class="btn btn-outline-secondary" for="quality-low">基本质量</label>

                                    <input type="radio" class="btn-check" name="vector-quality" id="quality-medium" autocomplete="off" checked value="medium">
                                    <label class="btn btn-outline-secondary" for="quality-medium">中等质量</label>

                                    <input type="radio" class="btn-check" name="vector-quality" id="quality-high" autocomplete="off" value="high">
                                    <label class="btn btn-outline-secondary" for="quality-high">高质量</label>
                                </div>
                            </div>
                            <div class="alert alert-warning mb-2 p-2 small">
                                <i class="fa fa-info-circle"></i> 点击开始处理按钮将<strong>立即扣除5积分</strong>，无论处理结果如何
                            </div>
                            <button class="btn btn-primary w-100" id="vector-process-btn" disabled {% if not vector_enabled or not has_win32 %}disabled="disabled" title="服务不可用"{% endif %}>生成矢量图</button>
                        </div>

                        <div class="file-info mt-3 small text-muted">
                            <p>支持的文件类型: JPG, PNG, JPEG, GIF</p>
                            <p>最大文件大小: 10MB</p>
                            <p>使用 Vector Magic 技术自动转换为高质量矢量图</p>
                        </div>
                    </div>
                </div>

                <!-- 处理状态 -->
                <div class="card shadow-sm mb-4" id="vector-processing-card" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">处理中</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <p id="vector-processing-message">正在生成矢量图，请稍候...</p>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div class="card shadow-sm mb-4" id="vector-result-card" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">处理结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>原图</h6>
                                <div class="original-image-container">
                                    <img id="vector-original-image" src="#" alt="原图" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>矢量图结果</h6>
                                <div class="result-image-container">
                                    <img id="vector-result-image" src="#" alt="矢量图结果" class="img-fluid rounded">
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <div class="btn-group">
                                <a id="vector-download-svg-btn" href="#" class="btn btn-primary" download>
                                    <i class="fa fa-download"></i> 下载SVG
                                </a>
                                <a id="vector-download-png-btn" href="#" class="btn btn-outline-primary" download>
                                    <i class="fa fa-download"></i> 下载PNG
                                </a>
                            </div>
                            <button id="vector-new-upload-btn" class="btn btn-outline-secondary ms-2">
                                <i class="fa fa-refresh"></i> 上传新图片
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="vector-error-alert" style="display: none;">
                    <strong>错误：</strong> <span id="vector-error-message"></span>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 地毯图案提取系统</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/vectorize.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示或隐藏管理员菜单
            if ('{{ user.role }}' === 'admin') {
                document.querySelectorAll('.admin-only').forEach(item => {
                    item.style.display = 'block';
                });
            }
            
            // 检查服务是否可用
            const vectorEnabled = {% if vector_enabled %}true{% else %}false{% endif %};
            const hasWin32 = {% if has_win32 %}true{% else %}false{% endif %};
            
            if (!vectorEnabled || !hasWin32) {
                // 禁用上传功能
                const uploadArea = document.getElementById('vector-upload-area');
                if (uploadArea) {
                    uploadArea.classList.add('disabled');
                    uploadArea.style.opacity = '0.6';
                    uploadArea.style.pointerEvents = 'none';
                }
                
                const processBtn = document.getElementById('vector-process-btn');
                if (processBtn) {
                    processBtn.disabled = true;
                    processBtn.classList.add('disabled');
                }
            }
        });
    </script>
</body>
</html> 