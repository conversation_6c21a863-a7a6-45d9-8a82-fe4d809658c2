#!/usr/bin/env python3
"""
Vector Magic配置测试脚本
用于测试Vector Magic的安装和配置是否正确
"""

import os
import sys
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_vector_magic_installation():
    """测试Vector Magic安装"""
    print("=" * 50)
    print("Vector Magic 安装测试")
    print("=" * 50)
    
    # 导入vectorize_service
    try:
        from vectorize_service import VectorizeService, VECTOR_MAGIC_PATH
        print("✓ vectorize_service模块导入成功")
    except ImportError as e:
        print(f"✗ vectorize_service模块导入失败: {e}")
        return False
    
    # 检查当前路径
    current_path = VectorizeService.get_current_path()
    print(f"当前配置路径: {current_path}")
    
    # 检查安装状态
    is_installed, message = VectorizeService.check_installation()
    if is_installed:
        print(f"✓ {message}")
    else:
        print(f"✗ {message}")
        return False
    
    # 测试可执行文件
    if current_path and os.path.exists(current_path):
        print(f"✓ 可执行文件存在: {current_path}")
        
        # 尝试获取版本信息（如果支持）
        try:
            result = subprocess.run([current_path, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ 版本信息: {result.stdout.strip()}")
            else:
                print("! 无法获取版本信息（这是正常的）")
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            print(f"! 无法执行版本检查: {e}")
    else:
        print(f"✗ 可执行文件不存在: {current_path}")
        return False
    
    # 检查Win32模块
    try:
        import win32api
        import win32con
        import win32gui
        print("✓ Win32模块可用")
    except ImportError:
        print("✗ Win32模块不可用，自动化功能将无法工作")
        return False
    
    # 检查pyautogui
    try:
        import pyautogui
        print("✓ PyAutoGUI模块可用")
    except ImportError:
        print("✗ PyAutoGUI模块不可用")
        return False
    
    print("\n" + "=" * 50)
    print("测试完成！Vector Magic配置正确。")
    print("=" * 50)
    return True

def interactive_path_setup():
    """交互式路径设置"""
    print("\n" + "=" * 50)
    print("Vector Magic 路径设置")
    print("=" * 50)
    
    common_paths = [
        r"C:\Program Files (x86)\Vector Magic\vmde.exe",
        r"C:\Program Files\Vector Magic\vmde.exe",
        r"C:\Program Files (x86)\Vector Magic\VectorMagic.exe",
        r"C:\Program Files\Vector Magic\VectorMagic.exe"
    ]
    
    print("常见安装路径:")
    for i, path in enumerate(common_paths, 1):
        exists = "✓" if os.path.exists(path) else "✗"
        print(f"{i}. {exists} {path}")
    
    print("\n选项:")
    print("1-4: 选择上述路径之一")
    print("c: 输入自定义路径")
    print("q: 退出")
    
    choice = input("\n请选择: ").strip().lower()
    
    if choice == 'q':
        return
    elif choice == 'c':
        custom_path = input("请输入Vector Magic可执行文件的完整路径: ").strip()
        if custom_path and os.path.exists(custom_path):
            set_path(custom_path)
        else:
            print("路径无效或文件不存在")
    elif choice.isdigit() and 1 <= int(choice) <= 4:
        selected_path = common_paths[int(choice) - 1]
        if os.path.exists(selected_path):
            set_path(selected_path)
        else:
            print("选择的路径不存在")
    else:
        print("无效选择")

def set_path(path):
    """设置Vector Magic路径"""
    try:
        from vectorize_service import VectorizeService
        success, message = VectorizeService.set_vector_magic_path(path)
        if success:
            print(f"✓ {message}")
        else:
            print(f"✗ {message}")
    except Exception as e:
        print(f"✗ 设置路径时出错: {e}")

def main():
    """主函数"""
    print("Vector Magic 配置测试工具")
    print("此工具用于测试和配置Vector Magic的安装")
    
    if len(sys.argv) > 1 and sys.argv[1] == '--setup':
        interactive_path_setup()
    else:
        success = test_vector_magic_installation()
        if not success:
            print("\n配置有问题，是否要进行路径设置？(y/n)")
            if input().strip().lower() == 'y':
                interactive_path_setup()

if __name__ == "__main__":
    main()
