#!/usr/bin/env python3
"""
Vector Magic 自动点击演示脚本
展示各种自动点击方案的使用方法
"""

import time
import pyautogui
import win32gui
import win32con
from vectorize_service import VectorizeService

def demo_basic_click():
    """演示基本坐标点击"""
    print("=" * 50)
    print("演示1: 基本坐标点击")
    print("=" * 50)
    
    print("这是最简单的点击方式，通过屏幕坐标点击")
    print("优点：实现简单")
    print("缺点：不够可靠，受分辨率和窗口位置影响")
    
    # 获取屏幕尺寸
    screen_width, screen_height = pyautogui.size()
    print(f"当前屏幕尺寸: {screen_width} x {screen_height}")
    
    # 示例：点击屏幕中心
    center_x = screen_width // 2
    center_y = screen_height // 2
    
    print(f"将在3秒后点击屏幕中心: ({center_x}, {center_y})")
    time.sleep(3)
    
    pyautogui.click(center_x, center_y)
    print("✓ 坐标点击完成")

def demo_image_recognition():
    """演示图像识别点击"""
    print("\n" + "=" * 50)
    print("演示2: 图像识别点击")
    print("=" * 50)
    
    print("通过识别按钮图像来精确定位和点击")
    print("优点：准确度高，不受位置影响")
    print("缺点：需要预先截取按钮图像")
    
    # 检查是否有按钮图像
    import os
    if os.path.exists("button_images"):
        button_files = [f for f in os.listdir("button_images") if f.endswith('.png')]
        if button_files:
            print(f"找到 {len(button_files)} 个按钮图像:")
            for btn in button_files:
                print(f"  - {btn}")
            
            # 尝试识别第一个按钮
            first_button = button_files[0]
            button_path = f"button_images/{first_button}"
            
            print(f"\n尝试识别按钮: {first_button}")
            try:
                location = pyautogui.locateOnScreen(button_path, confidence=0.8)
                if location:
                    center = pyautogui.center(location)
                    print(f"✓ 找到按钮位置: {center}")
                    
                    choice = input("是否点击此按钮? (y/n): ")
                    if choice.lower() == 'y':
                        pyautogui.click(center)
                        print("✓ 图像识别点击完成")
                else:
                    print("✗ 未在屏幕上找到按钮")
            except Exception as e:
                print(f"✗ 图像识别失败: {e}")
        else:
            print("button_images目录为空，请先使用button_capture_tool.py截取按钮")
    else:
        print("未找到button_images目录，请先使用button_capture_tool.py截取按钮")

def demo_color_detection():
    """演示颜色检测点击"""
    print("\n" + "=" * 50)
    print("演示3: 颜色检测点击")
    print("=" * 50)
    
    print("通过识别特定颜色的区域来定位按钮")
    print("优点：实现相对简单，适用于颜色鲜明的按钮")
    print("缺点：可能误识别其他相同颜色的元素")
    
    try:
        import cv2
        import numpy as np
        
        print("正在截取屏幕进行颜色分析...")
        
        # 截取屏幕
        screenshot = pyautogui.screenshot()
        screenshot_np = np.array(screenshot)
        screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
        hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)
        
        # 定义蓝色范围（常见的按钮颜色）
        blue_lower = np.array([100, 50, 50])
        blue_upper = np.array([130, 255, 255])
        
        # 创建掩码
        mask = cv2.inRange(hsv, blue_lower, blue_upper)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        button_candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 500:  # 过滤小区域
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                if 0.5 <= aspect_ratio <= 4.0:  # 按钮形状过滤
                    center_x = x + w // 2
                    center_y = y + h // 2
                    button_candidates.append((center_x, center_y, area))
        
        if button_candidates:
            print(f"找到 {len(button_candidates)} 个蓝色按钮候选:")
            for i, (x, y, area) in enumerate(button_candidates[:5]):  # 只显示前5个
                print(f"  {i+1}. 位置: ({x}, {y}), 面积: {area}")
            
            if len(button_candidates) > 0:
                choice = input("是否点击第一个候选按钮? (y/n): ")
                if choice.lower() == 'y':
                    x, y, _ = button_candidates[0]
                    pyautogui.click(x, y)
                    print("✓ 颜色检测点击完成")
        else:
            print("未找到符合条件的蓝色按钮")
            
    except ImportError:
        print("颜色检测需要opencv-python，请运行: pip install opencv-python")
    except Exception as e:
        print(f"颜色检测失败: {e}")

def demo_smart_detection():
    """演示智能综合检测"""
    print("\n" + "=" * 50)
    print("演示4: 智能综合检测")
    print("=" * 50)
    
    print("结合多种检测方法，提供最高的成功率")
    print("这是推荐的生产环境方案")
    
    # 查找Vector Magic窗口
    print("正在查找Vector Magic窗口...")
    
    vector_window = None
    def find_vector_magic_window(hwnd, ctx):
        try:
            window_text = win32gui.GetWindowText(hwnd)
            if "Vector Magic" in window_text or "vmde" in window_text.lower():
                nonlocal vector_window
                vector_window = hwnd
                return False
        except:
            pass
        return True
    
    win32gui.EnumWindows(find_vector_magic_window, None)
    
    if vector_window:
        window_text = win32gui.GetWindowText(vector_window)
        print(f"✓ 找到Vector Magic窗口: {window_text}")
        
        # 使用智能检测
        keywords = ["Auto", "Process", "Convert", "Go"]
        print(f"尝试智能检测按钮，关键词: {keywords}")
        
        try:
            success = VectorizeService._smart_button_detection(vector_window, keywords)
            if success:
                print("✓ 智能检测点击成功")
            else:
                print("✗ 智能检测未找到匹配的按钮")
        except Exception as e:
            print(f"✗ 智能检测失败: {e}")
    else:
        print("✗ 未找到Vector Magic窗口")
        print("请先启动Vector Magic软件")

def demo_ocr_detection():
    """演示OCR文本识别点击"""
    print("\n" + "=" * 50)
    print("演示5: OCR文本识别点击")
    print("=" * 50)
    
    print("通过OCR识别屏幕上的文字来定位按钮")
    print("优点：适用于文字按钮，支持多语言")
    print("缺点：需要安装Tesseract OCR引擎")
    
    try:
        import pytesseract
        from PIL import Image
        
        print("正在进行OCR文本识别...")
        
        # 截取屏幕
        screenshot = pyautogui.screenshot()
        
        # OCR识别
        ocr_data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
        
        # 查找常见按钮文字
        button_texts = ["OK", "Cancel", "Apply", "Save", "Open", "Close", "Auto", "Process"]
        found_buttons = []
        
        for i, text in enumerate(ocr_data['text']):
            if text.strip() and int(ocr_data['conf'][i]) > 50:
                for btn_text in button_texts:
                    if btn_text.lower() in text.lower():
                        x = ocr_data['left'][i] + ocr_data['width'][i] // 2
                        y = ocr_data['top'][i] + ocr_data['height'][i] // 2
                        confidence = ocr_data['conf'][i]
                        found_buttons.append((text, x, y, confidence))
        
        if found_buttons:
            print(f"找到 {len(found_buttons)} 个文字按钮:")
            for text, x, y, conf in found_buttons[:5]:  # 只显示前5个
                print(f"  '{text}' at ({x}, {y}), 置信度: {conf}%")
            
            if found_buttons:
                choice = input("是否点击第一个找到的按钮? (y/n): ")
                if choice.lower() == 'y':
                    _, x, y, _ = found_buttons[0]
                    pyautogui.click(x, y)
                    print("✓ OCR识别点击完成")
        else:
            print("未找到可识别的按钮文字")
            
    except ImportError:
        print("OCR功能需要安装pytesseract")
        print("请运行: pip install pytesseract")
        print("并安装Tesseract OCR引擎")
    except Exception as e:
        print(f"OCR识别失败: {e}")

def main():
    """主演示函数"""
    print("Vector Magic 自动点击方案演示")
    print("本演示将展示多种自动点击的实现方法")
    print("\n注意：演示过程中可能会实际点击屏幕，请确保没有重要操作正在进行")
    
    choice = input("\n是否继续演示? (y/n): ")
    if choice.lower() != 'y':
        print("演示已取消")
        return
    
    # 设置安全措施
    pyautogui.FAILSAFE = True
    print("\n安全提示：将鼠标移动到屏幕左上角可以中断操作")
    
    # 依次演示各种方案
    demos = [
        ("基本坐标点击", demo_basic_click),
        ("图像识别点击", demo_image_recognition),
        ("颜色检测点击", demo_color_detection),
        ("智能综合检测", demo_smart_detection),
        ("OCR文本识别点击", demo_ocr_detection)
    ]
    
    for name, demo_func in demos:
        print(f"\n准备演示: {name}")
        choice = input("是否执行此演示? (y/n/q): ")
        
        if choice.lower() == 'q':
            print("演示已退出")
            break
        elif choice.lower() == 'y':
            try:
                demo_func()
            except pyautogui.FailSafeException:
                print("检测到安全中断，演示已停止")
                break
            except Exception as e:
                print(f"演示过程中出错: {e}")
        else:
            print("跳过此演示")
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("=" * 50)
    print("\n推荐使用方案:")
    print("1. 生产环境：智能综合检测（最可靠）")
    print("2. 简单场景：图像识别点击（准确度高）")
    print("3. 调试阶段：使用real_time_detection.py工具")
    print("4. 按钮截图：使用button_capture_tool.py工具")

if __name__ == "__main__":
    main()
