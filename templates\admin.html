<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h4 {
            color: #fff;
            margin: 0;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            display: block;
            padding: 15px 25px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-item:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border-left-color: #3498db;
            transform: translateX(5px);
        }

        .menu-item.active {
            background: linear-gradient(90deg, rgba(52, 152, 219, 0.2) 0%, transparent 100%);
            color: #3498db;
            border-left-color: #3498db;
        }

        .menu-item i {
            width: 20px;
            margin-right: 12px;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }

        .content-header {
            background: #fff;
            padding: 25px 30px;
            border-bottom: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .content-body {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px 25px;
            font-weight: 600;
        }

        .btn {
            border-radius: 8px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            border: none;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 65, 108, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px 15px 0 0;
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-cogs me-2"></i>管理系统</h4>
        </div>
        <nav class="sidebar-menu">
            <a href="#" class="menu-item active" data-section="users">
                <i class="fas fa-users"></i>用户管理
            </a>
            <a href="#" class="menu-item" data-section="prompts">
                <i class="fas fa-comments"></i>提示词管理
            </a>
            <a href="#" class="menu-item" data-section="keys">
                <i class="fas fa-key"></i>密钥管理
            </a>
            <a href="#" class="menu-item" data-section="vector-magic">
                <i class="fas fa-vector-square"></i>矢量图配置
            </a>
            <a href="#" class="menu-item" data-section="settings">
                <i class="fas fa-cog"></i>系统设置
            </a>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0" id="page-title">用户管理</h2>
                <div>
                    <span class="me-3">欢迎，<span id="username-display">{{ user.username }}</span> <span class="badge bg-light text-dark">积分: <span id="user-points">{{ user.points }}</span></span></span>
                    <button class="btn btn-outline-primary d-md-none" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 用户管理部分 -->
            <div class="section active" id="users-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <div class="stats-number" id="total-users">156</div>
                            <div>总用户数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-user-check fa-2x mb-2"></i>
                            <div class="stats-number" id="active-users">142</div>
                            <div>活跃用户</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-user-shield fa-2x mb-2"></i>
                            <div class="stats-number" id="admin-users">8</div>
                            <div>管理员</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <div class="stats-number" id="total-api-calls">0</div>
                            <div>总调用次数</div>
                        </div>
                    </div>
                </div>

                <!-- 添加用户 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>添加新用户</h5>
                    </div>
                    <div class="card-body">
                        <form id="add-user-form">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">角色</label>
                                    <select class="form-select" id="role">
                                        <option value="user">普通用户</option>
                                        <option value="admin">管理员</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                <div class="col-md-6 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-plus me-2"></i>添加用户
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>用户列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>调用次数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="users-table">
                                    <!-- 示例数据 -->
                                    <tr>
                                        <td>1</td>
                                        <td>admin</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-danger">管理员</span></td>
                                        <td><span class="badge bg-success">活跃</span></td>
                                        <td>2024-01-15</td>
                                        <td>0</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary me-1" onclick="editUser(1)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteUser(1)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>张三</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-primary">普通用户</span></td>
                                        <td><span class="badge bg-success">活跃</span></td>
                                        <td>2024-02-20</td>
                                        <td>0</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary me-1" onclick="editUser(2)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteUser(2)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>李四</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-primary">普通用户</span></td>
                                        <td><span class="badge bg-warning">非活跃</span></td>
                                        <td>2024-03-10</td>
                                        <td>0</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary me-1" onclick="editUser(3)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteUser(3)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示词管理部分 -->
            <div class="section" id="prompts-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <div class="stats-number" id="total-prompts">1</div>
                            <div>提示词数量</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <div class="stats-number" id="active-prompts">1</div>
                            <div>活跃提示词</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <div class="stats-number" id="prompt-size">2.3 KB</div>
                            <div>提示词大小</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-history fa-2x mb-2"></i>
                            <div class="stats-number" id="usage-count">157</div>
                            <div>使用次数</div>
                        </div>
                    </div>
                </div>

                <!-- 添加提示词 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>添加新提示词</h5>
                    </div>
                    <div class="card-body">
                        <form id="add-prompt-form">
                            <div class="row">
                                <div class="col-md-9 mb-3">
                                    <label class="form-label">提示词名称</label>
                                    <input type="text" class="form-control" id="prompt-name" required>
                                    <div class="form-text">提示词名称必须唯一，建议使用描述性名称</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">设为默认</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" id="prompt-is-default">
                                        <label class="form-check-label" for="prompt-is-default">默认提示词</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">提示词内容</label>
                                <textarea class="form-control" id="prompt-content" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>添加提示词
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 提示词列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>提示词列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>内容预览</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="prompts-table">
                                    <!-- 示例数据 -->
                                    <tr>
                                        <td>1</td>
                                        <td>默认提示词</td>
                                        <td>请分析这张图片的内容...</td>
                                        <td><span class="badge bg-success">活跃</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <button class="btn btn-sm btn-info me-1" onclick="viewPrompt(1)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary me-1" onclick="editPrompt(1)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deletePrompt(1)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 密钥管理部分 -->
            <div class="section" id="keys-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-key fa-2x mb-2"></i>
                            <div class="stats-number" id="total-keys">0</div>
                            <div>密钥总数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <div class="stats-number" id="active-keys">0</div>
                            <div>可用密钥</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-times-circle fa-2x mb-2"></i>
                            <div class="stats-number" id="used-keys">0</div>
                            <div>已用密钥</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-coins fa-2x mb-2"></i>
                            <div class="stats-number" id="total-points">0</div>
                            <div>总积分</div>
                        </div>
                    </div>
                </div>

                <!-- 添加密钥 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>创建新密钥</h5>
                    </div>
                    <div class="card-body">
                        <form id="add-key-form">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">密钥</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="key-value" required>
                                        <button type="button" class="btn btn-outline-secondary" id="generate-key">
                                            <i class="fas fa-random"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">输入密钥或点击按钮自动生成</div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">积分</label>
                                    <input type="number" class="form-control" id="key-points" min="1" value="10" required>
                                    <div class="form-text">兑换获得的积分</div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">有效天数</label>
                                    <input type="number" class="form-control" id="key-days" min="1" value="30" required>
                                    <div class="form-text">延长的有效期</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">描述</label>
                                    <input type="text" class="form-control" id="key-description" placeholder="可选">
                                    <div class="form-text">密钥用途描述</div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>创建密钥
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 密钥列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>密钥列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>密钥</th>
                                        <th>积分</th>
                                        <th>有效天数</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>使用者</th>
                                        <th>使用时间</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="keys-table">
                                    <!-- 密钥数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vector Magic配置部分 -->
            <div class="section" id="vector-magic-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-vector-square fa-2x mb-2"></i>
                            <h3 id="vector-status-text">检查中...</h3>
                            <p>Vector Magic状态</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-folder-open fa-2x mb-2"></i>
                            <h3 id="vector-path-text">未设置</h3>
                            <p>安装路径</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <h3 id="vector-win32-text">检查中...</h3>
                            <p>Win32模块</p>
                        </div>
                    </div>
                </div>

                <!-- Vector Magic配置卡片 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-vector-square me-2"></i>Vector Magic配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>说明：</strong>Vector Magic是用于将位图转换为矢量图的专业软件。请确保已正确安装Vector Magic，并设置正确的可执行文件路径。
                                </div>

                                <!-- 当前状态 -->
                                <div class="mb-4">
                                    <h6>当前状态</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>安装状态：</label>
                                                <span id="vector-install-status" class="badge bg-secondary">检查中...</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>当前路径：</label>
                                                <code id="vector-current-path">未设置</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 路径配置 -->
                                <div class="mb-4">
                                    <h6>路径配置</h6>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <label for="vector-path-input">Vector Magic可执行文件路径：</label>
                                                <input type="text" class="form-control" id="vector-path-input"
                                                       placeholder="例如：C:\Program Files (x86)\Vector Magic\vmde.exe">
                                                <small class="form-text text-muted">
                                                    请输入Vector Magic可执行文件的完整路径。通常是vmde.exe文件。
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label>&nbsp;</label>
                                            <div>
                                                <button type="button" class="btn btn-primary" onclick="setVectorMagicPath()">
                                                    <i class="fas fa-save me-1"></i>设置路径
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" onclick="checkVectorMagicStatus()">
                                                    <i class="fas fa-sync me-1"></i>检查状态
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 常见路径提示 -->
                                <div class="mb-4">
                                    <h6>常见安装路径</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="list-unstyled">
                                                <li><code>C:\Program Files (x86)\Vector Magic\vmde.exe</code></li>
                                                <li><code>C:\Program Files\Vector Magic\vmde.exe</code></li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="list-unstyled">
                                                <li><code>C:\Program Files (x86)\Vector Magic\VectorMagic.exe</code></li>
                                                <li><code>C:\Program Files\Vector Magic\VectorMagic.exe</code></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- 测试功能 -->
                                <div class="mb-4">
                                    <h6>测试功能</h6>
                                    <p class="text-muted">配置完成后，您可以在矢量图生成页面测试功能是否正常工作。</p>
                                    <a href="/vectorize" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt me-1"></i>打开矢量图生成页面
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置部分 -->
            <div class="section" id="settings-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-key fa-2x mb-2"></i>
                            <div class="stats-number" id="api-key-status">未设置</div>
                            <div>API密钥状态</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <div class="stats-number" id="total-settings">0</div>
                            <div>系统设置总数</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <div class="stats-number" id="last-settings-update">-</div>
                            <div>最后更新时间</div>
                        </div>
                    </div>
                </div>

                <!-- API密钥设置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-key me-2"></i>API密钥设置</h5>
                    </div>
                    <div class="card-body">
                        <form id="api-key-form">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">OpenAI API密钥</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="openai-api-key" placeholder="sk-..." required>
                                    </div>
                                    <div class="form-text">输入您的OpenAI API密钥，用于图像处理服务</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>保存API密钥
                                    </button>
                                    <button type="button" class="btn btn-info ms-2" id="test-api-key">
                                        <i class="fas fa-vial me-2"></i>测试API密钥
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- API基础URL设置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-link me-2"></i>API基础URL设置</h5>
                    </div>
                    <div class="card-body">
                        <form id="api-base-form">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">OpenAI API基础URL</label>
                                    <input type="text" class="form-control" id="openai-api-base" placeholder="https://api.openai.com/v1" required>
                                    <div class="form-text">输入OpenAI API基础URL，默认为https://api.openai.com/v1，如果使用代理或自定义部署请修改</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>保存API基础URL
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-2" id="reset-api-base">
                                        <i class="fas fa-undo me-2"></i>重置为默认值
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 其他系统设置 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>其他系统设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>设置名称</th>
                                        <th>设置值</th>
                                        <th>描述</th>
                                        <th>更新时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="settings-table">
                                    <!-- 系统设置将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-user-form">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="edit-username" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="edit-email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-select" id="edit-role">
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">新密码 (留空表示不修改)</label>
                            <input type="password" class="form-control" id="edit-password">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看提示词模态框 -->
    <div class="modal fade" id="viewPromptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">查看提示词</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-control" id="view-prompt-name" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">内容</label>
                        <textarea class="form-control" id="view-prompt-content" rows="8" readonly></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="badge bg-info" id="view-prompt-default-badge" style="display: none;">默认提示词</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑提示词模态框 -->
    <div class="modal fade" id="editPromptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑提示词</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-prompt-form">
                        <input type="hidden" id="edit-prompt-id">
                        <div class="row">
                            <div class="col-md-9 mb-3">
                                <label class="form-label">名称</label>
                                <input type="text" class="form-control" id="edit-prompt-name">
                                <div class="form-text">提示词名称必须唯一</div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">设为默认</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="edit-prompt-is-default">
                                    <label class="form-check-label" for="edit-prompt-is-default">默认提示词</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">内容</label>
                            <textarea class="form-control" id="edit-prompt-content" rows="8"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePrompt()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户API调用统计模态框 -->
    <div class="modal fade" id="userApiStatsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户API调用统计</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="user-info mb-4">
                        <h5 id="api-stats-username">用户名</h5>
                        <p id="api-stats-email" class="text-muted">用户邮箱</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 id="api-stats-total" class="mb-0">0</h3>
                                    <p class="mb-0">总调用次数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 id="api-stats-success" class="mb-0">0</h3>
                                    <p class="mb-0">成功调用</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3 id="api-stats-error" class="mb-0">0</h3>
                                    <p class="mb-0">失败调用</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5>最近调用记录</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>API类型</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                    <th>提示词ID</th>
                                </tr>
                            </thead>
                            <tbody id="api-calls-table">
                                <tr>
                                    <td colspan="5" class="text-center">暂无调用记录</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面全局变量
        let currentUserId = null;
        let currentPromptId = null;
        let singlePromptMode = true;  // 标记使用单一提示词模式

        // 菜单切换功能
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活跃状态
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
                
                // 添加当前活跃状态
                this.classList.add('active');
                const section = this.getAttribute('data-section');
                document.getElementById(section + '-section').classList.add('active');
                
                // 更新页面标题
                const titles = {
                    'users': '用户管理',
                    'prompts': '提示词管理',
                    'keys': '密钥管理',
                    'vector-magic': '矢量图配置',
                    'settings': '系统设置'
                };
                document.getElementById('page-title').textContent = titles[section];

                // 加载对应区域的数据
                if (section === 'users') {
                    loadUsers();
                } else if (section === 'prompts') {
                    loadPrompts();
                } else if (section === 'keys') {
                    loadKeys();
                } else if (section === 'vector-magic') {
                    loadVectorMagicConfig();
                } else if (section === 'settings') {
                    loadSettings();
                }
            });
        });

        // 移动端侧边栏切换
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('mobile-open');
        }

        // 用户管理功能
        document.getElementById('add-user-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const role = document.getElementById('role').value;
            const password = document.getElementById('password').value;
            
            // 使用实际API添加用户
            fetch('/api/admin/users/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({username, email, password, role})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('用户添加成功！');
                    this.reset();
                    loadUsers();
                } else {
                    alert('添加用户失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('添加用户失败: ' + error.message);
                console.error('添加用户错误:', error);
            });
        });

        // 修改提示词表单，使用新的创建提示词API
        document.getElementById('add-prompt-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('prompt-name').value;
            const content = document.getElementById('prompt-content').value;
            const isDefault = document.getElementById('prompt-is-default').checked;
            
            if (!name.trim() || !content.trim()) {
                alert('提示词名称和内容不能为空');
                return;
            }
            
            // 使用系统创建提示词API
            fetch('/api/admin/prompts/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    name: name,
                    content: content,
                    is_default: isDefault
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('提示词创建成功！');
                    this.reset();
                    loadPrompts();
                } else {
                    alert('创建提示词失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('创建提示词失败: ' + error.message);
                console.error('创建提示词错误:', error);
            });
        });

        // 编辑用户
        function editUser(userId) {
            currentUserId = userId;
            
            // 获取用户数据 - 系统没有直接的单用户获取API，所以从用户列表中获取
            fetch(`/api/admin/users`, {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const user = data.data.users.find(u => u.id === userId);
                    if (user) {
                        document.getElementById('edit-username').value = user.username;
                        document.getElementById('edit-email').value = user.email;
                        document.getElementById('edit-role').value = user.role;
                        
                        // 显示模态框
                        new bootstrap.Modal(document.getElementById('editUserModal')).show();
                    } else {
                        alert('未找到用户数据');
                    }
                } else {
                    alert('获取用户数据失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('获取用户数据失败: ' + error.message);
                console.error('获取用户数据错误:', error);
            });
        }

        // 保存用户编辑
        function saveUser() {
            const email = document.getElementById('edit-email').value;
            const role = document.getElementById('edit-role').value;
            const password = document.getElementById('edit-password')?.value || ''; // 可选密码
            
            const userData = {
                email: email,
                role: role
            };
            
            // 如果输入了密码，则包含在更新数据中
            if (password.trim() !== '') {
                userData.password = password;
            }
            
            fetch(`/api/admin/users/${currentUserId}/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('用户更新成功！');
                    document.getElementById('editUserModal').querySelector('.btn-close').click();
                    loadUsers();
                } else {
                    alert('更新用户失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('更新用户失败: ' + error.message);
                console.error('更新用户错误:', error);
            });
        }

        // 删除用户
        function deleteUser(userId) {
            if (confirm('确定要删除此用户吗？此操作不可撤销。')) {
                fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('用户已成功删除！');
                        loadUsers();
                    } else {
                        alert('删除用户失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除用户失败: ' + error.message);
                    console.error('删除用户错误:', error);
                });
            }
        }

        // 查看提示词
        function viewPrompt(promptId) {
            fetch(`/api/admin/get-prompt?prompt_id=${promptId}`, {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 获取提示词详细信息
                    getPromptDetails(promptId).then(promptDetails => {
                        document.getElementById('view-prompt-name').value = promptDetails.name;
                        document.getElementById('view-prompt-content').value = data.data.prompt;
                        
                        // 显示默认提示词标记
                        const defaultBadge = document.getElementById('view-prompt-default-badge');
                        if (promptDetails.is_default) {
                            defaultBadge.style.display = 'inline-block';
                        } else {
                            defaultBadge.style.display = 'none';
                        }
                        
                        // 显示模态框
                        new bootstrap.Modal(document.getElementById('viewPromptModal')).show();
                    }).catch(error => {
                        alert('获取提示词详情失败: ' + error.message);
                    });
                } else {
                    alert('获取提示词数据失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('获取提示词数据失败: ' + error.message);
                console.error('获取提示词数据错误:', error);
            });
        }

        // 编辑提示词
        function editPrompt(promptId) {
            currentPromptId = promptId;
            
            fetch(`/api/admin/get-prompt?prompt_id=${promptId}`, {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 获取提示词详细信息
                    getPromptDetails(promptId).then(promptDetails => {
                        document.getElementById('edit-prompt-id').value = promptId;
                        document.getElementById('edit-prompt-name').value = promptDetails.name;
                        document.getElementById('edit-prompt-is-default').checked = promptDetails.is_default;
                        document.getElementById('edit-prompt-content').value = data.data.prompt;
                        
                        // 显示模态框
                        new bootstrap.Modal(document.getElementById('editPromptModal')).show();
                    }).catch(error => {
                        alert('获取提示词详情失败: ' + error.message);
                    });
                } else {
                    alert('获取提示词数据失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('获取提示词数据失败: ' + error.message);
                console.error('获取提示词数据错误:', error);
            });
        }

        // 保存提示词编辑
        function savePrompt() {
            const promptId = document.getElementById('edit-prompt-id').value;
            const newName = document.getElementById('edit-prompt-name').value;
            const content = document.getElementById('edit-prompt-content').value;
            const setDefault = document.getElementById('edit-prompt-is-default').checked;
            
            if (!content.trim()) {
                alert('提示词内容不能为空');
                return;
            }
            
            // 使用更新API
            fetch(`/api/admin/update-prompt`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    prompt_id: promptId,
                    new_prompt: content,
                    new_name: newName,
                    set_default: setDefault
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('提示词更新成功！');
                    document.getElementById('editPromptModal').querySelector('.btn-close').click();
                    loadPrompts();
                } else {
                    alert('更新提示词失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('更新提示词失败: ' + error.message);
                console.error('更新提示词错误:', error);
            });
        }

        // 删除提示词
        function deletePrompt(promptId) {
            if (confirm('确定要删除此提示词吗？此操作不可撤销。')) {
                fetch(`/api/admin/prompts/${promptId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('提示词已成功删除！');
                        loadPrompts();
                    } else {
                        alert('删除提示词失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除提示词失败: ' + error.message);
                    console.error('删除提示词错误:', error);
                });
            }
        }

        // 设置默认提示词
        function setDefaultPrompt(promptId) {
            fetch(`/api/admin/prompts/${promptId}/set-default`, {
                method: 'POST',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('默认提示词已设置！');
                    loadPrompts();
                } else {
                    alert('设置默认提示词失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('设置默认提示词失败: ' + error.message);
                console.error('设置默认提示词错误:', error);
            });
        }

        // 加载用户数据
        function loadUsers() {
            fetch('/api/admin/users', {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新统计信息
                    updateUserStats(data.data.users);
                    // 渲染用户表格
                    renderUsersTable(data.data.users);
                } else {
                    console.error('加载用户数据失败:', data.message);
                    alert('加载用户数据失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('加载用户数据错误:', error);
                alert('加载用户数据失败: ' + error.message);
            });
        }

        // 加载提示词列表
        function loadPrompts() {
            fetch('/api/admin/prompts', {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新提示词统计信息
                    updatePromptStats(data.data.prompts, data.data.stats);
                    // 渲染提示词表格
                    renderPromptsTable(data.data.prompts);
                } else {
                    console.error('加载提示词数据失败:', data.message);
                    alert('加载提示词数据失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('加载提示词数据错误:', error);
                alert('加载提示词数据失败: ' + error.message);
            });
        }

        // 更新用户统计信息 - 根据真实数据计算
        function updateUserStats(users) {
            if (!users) return;
            
            const totalUsers = users.length;
            const activeUsers = users.filter(user => user.is_active).length;
            const adminUsers = users.filter(user => user.role === 'admin').length;
            
            // 计算总调用次数
            let totalApiCalls = 0;
            users.forEach(user => {
                if (user.api_calls) {
                    totalApiCalls += user.api_calls.total_calls;
                }
            });
            
            // 更新页面上的统计信息
            document.getElementById('total-users').textContent = totalUsers;
            document.getElementById('active-users').textContent = activeUsers;
            document.getElementById('admin-users').textContent = adminUsers;
            document.getElementById('total-api-calls').textContent = totalApiCalls;
        }

        // 更新提示词统计信息
        function updatePromptStats(prompts, stats) {
            // 统计数据
            document.getElementById('total-prompts').textContent = stats.total_prompts || prompts.length || 0;
            document.getElementById('active-prompts').textContent = prompts.length || 0;
            
            // 计算提示词总大小
            let totalSize = 0;
            prompts.forEach(prompt => {
                // 估计每个字符约2字节
                totalSize += prompt.content_preview.length * 2;
            });
            
            let sizeStr = '';
            if (totalSize > 1024 * 1024) {
                sizeStr = (totalSize / (1024 * 1024)).toFixed(2) + ' MB';
            } else if (totalSize > 1024) {
                sizeStr = (totalSize / 1024).toFixed(2) + ' KB';
            } else {
                sizeStr = totalSize + ' B';
            }
            
            document.getElementById('prompt-size').textContent = sizeStr;
            
            // 使用次数 - 模拟数据
            document.getElementById('usage-count').textContent = '157';
        }

        // 渲染用户表格
        function renderUsersTable(users) {
            if (!users) return;
            
            const tableBody = document.getElementById('users-table');
            tableBody.innerHTML = '';
            
            if (users.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="8" class="text-center">没有找到任何用户</td>`;
                tableBody.appendChild(row);
                return;
            }
            
            // 添加用户数据
            users.forEach(user => {
                const row = document.createElement('tr');
                const createdAt = new Date(user.created_at).toLocaleDateString('zh-CN');
                const apiCalls = user.api_calls ? user.api_calls.total_calls : 0;
                
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-primary'}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                    <td><span class="badge ${user.is_active ? 'bg-success' : 'bg-warning'}">${user.is_active ? '活跃' : '非活跃'}</span></td>
                    <td>${createdAt}</td>
                    <td>
                        <span class="badge bg-info">${apiCalls}</span>
                        <button class="btn btn-sm btn-outline-info ms-1" onclick="viewUserApiStats('${user.id}')" title="查看详情">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editUser('${user.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }

        // 渲染提示词表格
        function renderPromptsTable(prompts) {
            const tableBody = document.getElementById('prompts-table');
            if (!tableBody) return;
            
            // 清空表格
            tableBody.innerHTML = '';
            
            if (prompts.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="6" class="text-center">没有提示词数据</td>`;
                tableBody.appendChild(row);
                return;
            }
            
            // 添加提示词数据
            prompts.forEach(prompt => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${prompt.id}</td>
                    <td>
                        ${prompt.name}
                        ${prompt.is_default ? '<span class="badge bg-info ms-2">默认</span>' : ''}
                    </td>
                    <td>${prompt.content_preview}</td>
                    <td><span class="badge bg-success">活跃</span></td>
                    <td>${formatDate(prompt.created_at)}</td>
                    <td>
                        <button class="btn btn-sm btn-info me-1" onclick="viewPrompt(${prompt.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary me-1" onclick="editPrompt(${prompt.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${!prompt.is_default ? `
                            <button class="btn btn-sm btn-warning me-1" onclick="setDefaultPrompt(${prompt.id})" title="设为默认">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deletePrompt(${prompt.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 获取提示词详细信息
        async function getPromptDetails(promptId) {
            try {
                const response = await fetch('/api/admin/prompts', { credentials: 'include' });
                const data = await response.json();
                
                if (data.success && data.data && data.data.prompts) {
                    const prompt = data.data.prompts.find(p => p.id === parseInt(promptId));
                    if (prompt) {
                        return prompt;
                    } else {
                        throw new Error('找不到提示词');
                    }
                } else {
                    throw new Error(data.message || '获取提示词列表失败');
                }
            } catch (error) {
                console.error('获取提示词详情错误:', error);
                throw error;
            }
        }

        // 辅助函数
        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('zh-CN');
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function getCategoryClass(category) {
            const classes = {
                'default': 'bg-info',
                'creative': 'bg-warning',
                'business': 'bg-success',
                'technical': 'bg-primary',
                'custom': 'bg-secondary'
            };
            return classes[category] || 'bg-secondary';
        }

        // 初始加载
        document.addEventListener('DOMContentLoaded', () => {
            loadUsers();
            
            // 预加载系统设置，确保API密钥状态显示正确
            loadSettings();
            
            // 加载密钥列表
            loadKeys();
            
            // 添加用户状态切换功能
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('toggle-status')) {
                    const userId = e.target.getAttribute('data-id');
                    if (userId) {
                        toggleUserStatus(userId);
                    }
                }
            });
            
            // 添加生成随机密钥功能
            document.getElementById('generate-key')?.addEventListener('click', function() {
                document.getElementById('key-value').value = generateRandomKey();
            });
            
            // 添加创建密钥表单提交事件
            document.getElementById('add-key-form')?.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const keyData = {
                    redeem_key: document.getElementById('key-value').value,
                    points: parseInt(document.getElementById('key-points').value),
                    days: parseInt(document.getElementById('key-days').value),
                    description: document.getElementById('key-description').value
                };
                
                try {
                    const response = await fetch('/api/admin/create-key', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify(keyData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('密钥创建成功！');
                        
                        // 清空表单
                        document.getElementById('key-value').value = '';
                        document.getElementById('key-description').value = '';
                        
                        // 重新加载密钥列表
                        loadKeys();
                    } else {
                        alert('创建密钥失败: ' + data.message);
                    }
                } catch (error) {
                    console.error('创建密钥错误:', error);
                    alert('创建密钥时发生错误: ' + error.message);
                }
            });
        });
        
        // 切换用户状态
        function toggleUserStatus(userId) {
            fetch(`/api/admin/users/${userId}/toggle-status`, {
                method: 'POST',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message || '用户状态已更新');
                    loadUsers();
                } else {
                    alert('更新用户状态失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('更新用户状态失败: ' + error.message);
                console.error('更新用户状态错误:', error);
            });
        }
        
        // 查看用户API调用统计
        function viewUserApiStats(userId) {
            fetch(`/api/admin/users/${userId}/api-stats`, {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const user = data.data.user;
                    const apiStats = data.data.api_stats;
                    const recentCalls = data.data.recent_calls;
                    
                    // 更新用户信息
                    document.getElementById('api-stats-username').textContent = user.username;
                    document.getElementById('api-stats-email').textContent = user.email;
                    
                    // 更新统计数据
                    document.getElementById('api-stats-total').textContent = apiStats.total_calls;
                    document.getElementById('api-stats-success').textContent = apiStats.success_calls;
                    document.getElementById('api-stats-error').textContent = apiStats.error_calls;
                    
                    // 更新调用记录表格
                    const tableBody = document.getElementById('api-calls-table');
                    tableBody.innerHTML = '';
                    
                    if (recentCalls && recentCalls.length > 0) {
                        recentCalls.forEach(call => {
                            const row = document.createElement('tr');
                            const callTime = new Date(call.created_at).toLocaleString('zh-CN');
                            
                            row.innerHTML = `
                                <td>${call.id}</td>
                                <td>${call.api_type}</td>
                                <td>${callTime}</td>
                                <td>
                                    <span class="badge ${call.status === 'success' ? 'bg-success' : 'bg-danger'}">
                                        ${call.status === 'success' ? '成功' : '失败'}
                                    </span>
                                </td>
                                <td>${call.prompt_id || '-'}</td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">暂无调用记录</td></tr>';
                    }
                    
                    // 显示模态框
                    new bootstrap.Modal(document.getElementById('userApiStatsModal')).show();
                } else {
                    alert('获取用户API调用统计失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('获取用户API调用统计失败: ' + error.message);
                console.error('获取用户API调用统计错误:', error);
            });
        }

        // API密钥表单提交事件
        document.getElementById('api-key-form')?.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('openai-api-key').value;
            
            if (!apiKey) {
                alert('API密钥不能为空');
                return;
            }
            
            try {
                const response = await fetch('/api/admin/settings/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        key: 'openai_api_key',
                        value: apiKey,
                        description: 'OpenAI API密钥，用于图像处理服务'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('API密钥已成功保存到数据库');
                    loadSettings();
                } else {
                    alert('保存API密钥失败: ' + result.message);
                }
            } catch (error) {
                console.error('保存API密钥错误:', error);
                alert('保存API密钥时发生错误');
            }
        });

        // API密钥现在使用明文显示，不需要切换可见性

        // 测试API密钥
        document.getElementById('test-api-key')?.addEventListener('click', async function() {
            // 从数据库中获取API密钥进行测试，不使用表单中的值
            this.disabled = true;
            const originalHTML = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>测试中...';
            
            try {
                const response = await fetch('/api/admin/test-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('API密钥测试成功！API密钥有效，可以正常使用。');
                } else {
                    // 显示具体的错误信息
                    let errorMsg = '无效的API密钥或API基础URL。';
                    if (result.data && result.data.error) {
                        errorMsg += '\n\n详细错误: ' + result.data.error;
                    }
                    alert('API密钥测试失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('测试API密钥错误:', error);
                alert('测试API密钥时发生错误: ' + error.message);
            } finally {
                this.disabled = false;
                this.innerHTML = originalHTML;
            }
        });

        // API基础URL表单提交
        document.getElementById('api-base-form')?.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiBase = document.getElementById('openai-api-base').value;
            
            if (!apiBase) {
                alert('API基础URL不能为空');
                return;
            }
            
            try {
                const response = await fetch('/api/admin/settings/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        key: 'openai_api_base',
                        value: apiBase,
                        description: 'OpenAI API基础URL，用于图像处理服务'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('API基础URL已成功保存到数据库');
                    loadSettings();
                } else {
                    alert('保存API基础URL失败: ' + result.message);
                }
            } catch (error) {
                console.error('保存API基础URL错误:', error);
                alert('保存API基础URL时发生错误');
            }
        });

        // 重置API基础URL
        document.getElementById('reset-api-base')?.addEventListener('click', async function() {
            const defaultApiBase = 'https://api.openai.com/v1';
            document.getElementById('openai-api-base').value = defaultApiBase;
            
            try {
                const response = await fetch('/api/admin/settings/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        key: 'openai_api_base',
                        value: defaultApiBase,
                        description: 'OpenAI API基础URL，用于图像处理服务'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('API基础URL已重置为默认值并保存到数据库');
                    loadSettings();
                } else {
                    alert('重置API基础URL失败: ' + result.message);
                }
            } catch (error) {
                console.error('重置API基础URL错误:', error);
                alert('重置API基础URL时发生错误');
            }
        });
        
        // 加载系统设置
        async function loadSettings() {
            try {
                const response = await fetch('/api/admin/settings', {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (data.success && data.data && data.data.settings) {
                    // 更新系统设置统计
                    document.getElementById('total-settings').textContent = data.data.settings.length;
                    
                    // 查找最后更新时间
                    let lastUpdate = null;
                    
                    // 填充API密钥和API基础URL表单
                    data.data.settings.forEach(setting => {
                        // 检查最后更新时间
                        if (!lastUpdate || new Date(setting.updated_at) > new Date(lastUpdate)) {
                            lastUpdate = setting.updated_at;
                        }
                        
                        // 获取API密钥状态和值
                        if (setting.key === 'openai_api_key') {
                            document.getElementById('api-key-status').textContent = 
                                setting.value ? '已设置' : '未设置';
                            // 回显API密钥到输入框（使用原始值，不脱敏）
                            if (setting.value) {
                                document.getElementById('openai-api-key').value = setting.value;
                            }
                        }
                        
                        // 填充API基础URL
                        if (setting.key === 'openai_api_base' && setting.value) {
                            document.getElementById('openai-api-base').value = setting.value;
                        }
                    });
                    
                    // 更新最后更新时间
                    document.getElementById('last-settings-update').textContent = 
                        lastUpdate ? new Date(lastUpdate).toLocaleString('zh-CN') : '-';
                    
                    // 渲染设置表格
                    renderSettingsTable(data.data.settings);
                } else {
                    console.error('加载系统设置失败:', data.message);
                }
            } catch (error) {
                console.error('加载系统设置错误:', error);
            }
        }
        
        // 渲染设置表格
        function renderSettingsTable(settings) {
            const settingsTable = document.getElementById('settings-table');
            if (!settingsTable) return;
            
            settingsTable.innerHTML = '';
            
            // 过滤掉API密钥和API基础URL，因为它们已经有专门的设置表单
            const filteredSettings = settings.filter(setting => 
                setting.key !== 'openai_api_key' && setting.key !== 'openai_api_base');
            
            if (filteredSettings.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">没有其他系统设置</td>';
                settingsTable.appendChild(row);
                return;
            }
            
            // 渲染其他设置
            filteredSettings.forEach(setting => {
                const row = document.createElement('tr');
                const updatedAt = setting.updated_at ? new Date(setting.updated_at).toLocaleString('zh-CN') : '-';
                
                row.innerHTML = `
                    <td>${setting.key}</td>
                    <td>${setting.value}</td>
                    <td>${setting.description || '-'}</td>
                    <td>${updatedAt}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editSetting('${setting.key}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;
                
                settingsTable.appendChild(row);
            });
        }
        
        // 编辑设置
        function editSetting(key) {
            // 这里可以添加编辑其他设置的功能
            alert('编辑设置功能未实现');
        }
        
        // 加载密钥列表
        async function loadKeys() {
            try {
                const response = await fetch('/api/admin/redeem-keys', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success && data.data && data.data.keys) {
                        // 更新密钥统计信息
                        updateKeysStats(data.data.keys);
                        // 渲染密钥表格
                        renderKeysTable(data.data.keys);
                    } else {
                        console.error('获取密钥列表数据格式错误:', data.message);
                    }
                } else {
                    console.error('获取密钥列表失败');
                }
            } catch (error) {
                console.error('加载密钥列表错误:', error);
            }
        }
        
        // 更新密钥统计信息
        function updateKeysStats(keys) {
            if (!keys) return;
            
            const totalKeys = keys.length;
            const activeKeys = keys.filter(key => !key.used).length;
            const usedKeys = keys.filter(key => key.used).length;
            
            // 计算总积分
            let totalPoints = 0;
            keys.forEach(key => {
                totalPoints += parseInt(key.points) || 0;
            });
            
            // 更新页面上的统计信息
            document.getElementById('total-keys').textContent = totalKeys;
            document.getElementById('active-keys').textContent = activeKeys;
            document.getElementById('used-keys').textContent = usedKeys;
            document.getElementById('total-points').textContent = totalPoints;
        }
        
        // 渲染密钥表格
        function renderKeysTable(keys) {
            const tableBody = document.getElementById('keys-table');
            if (!tableBody) return;
            
            // 清空表格内容
            tableBody.innerHTML = '';
            
            if (keys.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="9" class="text-center">没有找到任何密钥</td>`;
                tableBody.appendChild(row);
                return;
            }
            
            // 添加密钥行
            keys.forEach(key => {
                const row = document.createElement('tr');
                
                // 格式化日期
                const createdAt = new Date(key.created_at).toLocaleString('zh-CN');
                const usedAt = key.used_at ? new Date(key.used_at).toLocaleString('zh-CN') : '-';
                
                // 根据状态显示不同的标签
                const statusBadge = key.used 
                    ? `<span class="badge bg-danger">已使用</span>` 
                    : `<span class="badge bg-success">可用</span>`;
                
                // 操作按钮 - 只有未使用的密钥可以删除
                const actionButtons = key.used 
                    ? '-' 
                    : `<button class="btn btn-sm btn-danger" onclick="deleteKey('${key.redeem_key}')">
                        <i class="fas fa-trash"></i>
                      </button>`;
                
                row.innerHTML = `
                    <td>${key.redeem_key}</td>
                    <td>${key.points}</td>
                    <td>${key.days}</td>
                    <td>${key.description || '-'}</td>
                    <td>${statusBadge}</td>
                    <td>${key.used_by || '-'}</td>
                    <td>${usedAt}</td>
                    <td>${createdAt}</td>
                    <td>${actionButtons}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 生成随机密钥
        function generateRandomKey(length = 16) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
        
        // 删除密钥
        async function deleteKey(redeemKey) {
            if (confirm('确定要删除此密钥吗？此操作不可撤销。')) {
                try {
                    const response = await fetch(`/api/admin/delete-key/${redeemKey}`, {
                        method: 'DELETE',
                        credentials: 'include'
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('密钥已成功删除！');
                        loadKeys(); // 重新加载密钥列表
                    } else {
                        alert('删除密钥失败: ' + data.message);
                    }
                } catch (error) {
                    console.error('删除密钥错误:', error);
                    alert('删除密钥时发生错误: ' + error.message);
                }
            }
        }

        // Vector Magic配置相关函数
        async function loadVectorMagicConfig() {
            try {
                await checkVectorMagicStatus();
            } catch (error) {
                console.error('加载Vector Magic配置错误:', error);
            }
        }

        async function checkVectorMagicStatus() {
            try {
                const response = await fetch('/api/admin/vector-magic/status', {
                    method: 'GET',
                    credentials: 'include'
                });

                const data = await response.json();

                if (data.success) {
                    // 更新状态显示
                    document.getElementById('vector-status-text').textContent = '已安装';
                    document.getElementById('vector-install-status').textContent = '已安装';
                    document.getElementById('vector-install-status').className = 'badge bg-success';

                    // 更新路径显示
                    const currentPath = data.data.current_path || '未设置';
                    document.getElementById('vector-path-text').textContent = currentPath.length > 20 ?
                        '...' + currentPath.slice(-20) : currentPath;
                    document.getElementById('vector-current-path').textContent = currentPath;
                    document.getElementById('vector-path-input').value = currentPath;

                    // 更新Win32状态
                    document.getElementById('vector-win32-text').textContent = data.data.has_win32 ? '已安装' : '未安装';
                } else {
                    // 更新状态显示为未安装
                    document.getElementById('vector-status-text').textContent = '未安装';
                    document.getElementById('vector-install-status').textContent = '未安装';
                    document.getElementById('vector-install-status').className = 'badge bg-danger';

                    document.getElementById('vector-path-text').textContent = '未设置';
                    document.getElementById('vector-current-path').textContent = data.message || '未设置';

                    document.getElementById('vector-win32-text').textContent = '未知';
                }
            } catch (error) {
                console.error('检查Vector Magic状态错误:', error);
                document.getElementById('vector-status-text').textContent = '检查失败';
                document.getElementById('vector-install-status').textContent = '检查失败';
                document.getElementById('vector-install-status').className = 'badge bg-warning';
            }
        }

        async function setVectorMagicPath() {
            const pathInput = document.getElementById('vector-path-input');
            const newPath = pathInput.value.trim();

            if (!newPath) {
                alert('请输入Vector Magic可执行文件路径');
                return;
            }

            try {
                const response = await fetch('/api/admin/vector-magic/set-path', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        path: newPath
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('Vector Magic路径设置成功！');
                    await checkVectorMagicStatus(); // 重新检查状态
                } else {
                    alert('设置路径失败: ' + data.message);
                }
            } catch (error) {
                console.error('设置Vector Magic路径错误:', error);
                alert('设置路径时发生错误: ' + error.message);
            }
        }
    </script>
</body>
</html>