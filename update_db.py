import os
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def update_database():
    """更新数据库表结构"""
    try:
        db_path = "frmaster.db"
        
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return False
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查user_api_calls表是否存在result_file列
        cursor.execute("PRAGMA table_info(user_api_calls)")
        columns = [info[1] for info in cursor.fetchall()]
        
        if "result_file" not in columns:
            logger.info("添加result_file列到user_api_calls表")
            cursor.execute("ALTER TABLE user_api_calls ADD COLUMN result_file TEXT")
            conn.commit()
            logger.info("添加result_file列成功")
        else:
            logger.info("result_file列已存在，无需添加")
        
        # 检查users表是否存在points和expiry_date列
        cursor.execute("PRAGMA table_info(users)")
        user_columns = [info[1] for info in cursor.fetchall()]
        
        # 添加积分字段
        if "points" not in user_columns:
            logger.info("添加points列到users表")
            cursor.execute("ALTER TABLE users ADD COLUMN points INTEGER NOT NULL DEFAULT 0")
            conn.commit()
            logger.info("添加points列成功")
        else:
            logger.info("points列已存在，无需添加")
            
        # 添加过期时间字段
        if "expiry_date" not in user_columns:
            logger.info("添加expiry_date列到users表")
            cursor.execute("ALTER TABLE users ADD COLUMN expiry_date TEXT")
            conn.commit()
            logger.info("添加expiry_date列成功")
        else:
            logger.info("expiry_date列已存在，无需添加")
        
        # 检查redeem_keys表是否存在，不存在则创建
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='redeem_keys'")
        if not cursor.fetchone():
            logger.info("创建redeem_keys表")
            cursor.execute('''
            CREATE TABLE redeem_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                redeem_key TEXT UNIQUE NOT NULL,
                points INTEGER NOT NULL,
                days INTEGER NOT NULL,
                description TEXT,
                used INTEGER DEFAULT 0,
                used_by TEXT,
                used_at TEXT,
                created_at TEXT NOT NULL
            )
            ''')
            conn.commit()
            logger.info("redeem_keys表创建成功")
        else:
            logger.info("redeem_keys表已存在，无需创建")
        
        conn.close()
        return True
    
    except Exception as e:
        logger.error(f"更新数据库时出错: {str(e)}")
        return False

if __name__ == "__main__":
    if update_database():
        logger.info("数据库更新成功")
    else:
        logger.error("数据库更新失败") 