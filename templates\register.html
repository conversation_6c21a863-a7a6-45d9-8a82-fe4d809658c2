<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 地毯图案提取系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
</head>
<body>
    <div class="container">
        <header class="my-4 text-center">
            <h1>地毯图案提取系统</h1>
            <p class="lead">新用户注册</p>
        </header>
        
        <div class="row">
            <div class="col-md-6 col-lg-5 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">创建账号</h5>
                    </div>
                    <div class="card-body">
                        <!-- 注册表单 -->
                        <form id="register-form">
                            <!-- 用户名 -->
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            
                            <!-- 邮箱 -->
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <!-- 密码 -->
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <!-- 确认密码 -->
                            <div class="mb-3">
                                <label for="confirm-password" class="form-label">确认密码</label>
                                <input type="password" class="form-control" id="confirm-password" name="confirm-password" required>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">注册</button>
                            </div>
                            
                            <!-- 成功/错误信息显示 -->
                            <div class="alert alert-success mt-3" id="success-message" style="display: none;"></div>
                            <div class="alert alert-danger mt-3" id="error-message" style="display: none;"></div>
                        </form>
                        
                        <!-- 登录链接 -->
                        <div class="mt-3 text-center">
                            <p>已有账号？ <a href="/login">立即登录</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 地毯图案提取系统</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('register-form');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');
            
            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // 隐藏消息
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';
                
                // 获取表单数据
                const username = document.getElementById('username').value;
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm-password').value;
                
                // 验证密码一致性
                if (password !== confirmPassword) {
                    errorMessage.textContent = '两次输入的密码不一致';
                    errorMessage.style.display = 'block';
                    return;
                }
                
                // 准备请求数据
                const userData = {
                    username,
                    email,
                    password
                };
                
                try {
                    // 发送注册请求
                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(userData)
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        // 注册成功
                        successMessage.textContent = '账号创建成功，即将跳转到登录页面...';
                        successMessage.style.display = 'block';
                        
                        // 清空表单
                        registerForm.reset();
                        
                        // 3秒后跳转到登录页面
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 3000);
                    } else {
                        // 注册失败
                        errorMessage.textContent = data.message || '注册失败，请稍后再试';
                        errorMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('注册错误:', error);
                    errorMessage.textContent = '注册过程中发生错误，请稍后再试';
                    errorMessage.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html> 