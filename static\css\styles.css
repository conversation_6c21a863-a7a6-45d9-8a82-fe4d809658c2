/* 通用样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

.container {
    max-width: 1200px;
}

/* 图片上传区域样式 */
.upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e9f2ff;
}

.upload-prompt {
    padding: 30px 10px;
}

.preview-area {
    padding: 10px;
}

.preview-area img {
    max-height: 200px;
    max-width: 100%;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 结果项样式 */
.result-item {
    margin-bottom: 30px;
    padding-bottom: 20px;
}

.original-image-container,
.result-image-container {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    background-color: #f9f9f9;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.original-image-container img,
.result-image-container img {
    max-height: 300px;
    max-width: 100%;
    object-fit: contain;
}

/* 处理中状态 */
.progress {
    height: 8px;
}

/* 按钮样式 */
.add-more-container {
    margin: 20px 0;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .result-item .row {
        flex-direction: column;
    }
    .result-item .col-md-6 {
        margin-bottom: 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 500;
}

/* 页脚样式 */
.footer {
    margin-top: 3rem;
    border-top: 1px solid #dee2e6;
} 