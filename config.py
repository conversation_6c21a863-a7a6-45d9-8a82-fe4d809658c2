import os
from dotenv import load_dotenv
from pathlib import Path
import secrets

# 加载环境变量
load_dotenv()

# 基本目录配置
BASE_DIR = Path(__file__).resolve().parent
UPLOAD_FOLDER = os.path.join(BASE_DIR, os.getenv("UPLOAD_FOLDER", "uploads"))
OUTPUT_FOLDER = os.path.join(BASE_DIR, os.getenv("OUTPUT_FOLDER", "outputs"))
PROMPT_FOLDER = os.path.join(BASE_DIR, os.getenv("PROMPT_FOLDER", "prompts"))
PROMPT_FILE = os.path.join(PROMPT_FOLDER, "default_prompt.txt")

# 服务器配置
HOST = os.getenv("HOST", "127.0.0.1")
PORT = int(os.getenv("PORT", 8001))
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# API密钥配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
ADMIN_API_KEY = "********************************************************************************************************************************************************************"
ADMIN_TOKEN = os.getenv("ADMIN_TOKEN", "admin-token-123456")

# JWT配置
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", secrets.token_hex(32))
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 30))

# 文件上传配置
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", 10485760))  # 默认10MB
ALLOWED_EXTENSIONS = os.getenv("ALLOWED_EXTENSIONS", "jpg,jpeg,png,gif").split(",")

# GPT配置
GPT_MODEL = os.getenv("GPT_MODEL", "gpt-image-1")  # 确保默认使用支持的图像模型
MAX_RETRIES = int(os.getenv("MAX_RETRIES", 3))
TIMEOUT = int(os.getenv("TIMEOUT", 180))

# 创建必要的目录
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, PROMPT_FOLDER]:
    try:
        os.makedirs(folder, exist_ok=True)
        # 确保目录有读写权限
        os.chmod(folder, 0o755)  # rwxr-xr-x
        print(f"目录已创建并设置权限: {folder}")
    except Exception as e:
        print(f"创建目录或设置权限失败: {folder}, 错误: {e}")

# 默认提示词（加密存储到文件中，这里仅为初始化）
DEFAULT_PROMPT = """
1. 精确提取：从提供的地毯产品图片中，严格提取图案地毯的图案，不包括地毯外的图案，且完全去除所有背景和其他元素，同时保留内部设计。
2. 将图案设计的样式和内容元素还原为填充整个画面的平面印刷图像。
3. 完整识别：准确识别并完整还原地毯中的图案、图形等设计元素，确保没有任何遗漏或失真。
4. 无需确认步骤：根据这些要求直接生成图片，无需任何形式的确认或疑问。
5. 自动重试：如果生成失败，则自动重新生成，直到获得满意的结果。
6. 重要提示：请勿包含产品本身（例如杯子、衣服、水壶、或任何其他载体物品）。
7. 背景要求：请勿使用透明或带边框背景。
8. 请确保生成出来的图片比例是3:2的。
9. 绘图式方式为矢量
10. 生成图片分辨率3000*2000像素
11. 地毯的颜色要和图片一致
12. 不要底纹
""" 