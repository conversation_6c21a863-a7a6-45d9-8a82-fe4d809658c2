document.addEventListener("DOMContentLoaded", function() {
    // 获取DOM元素
    const loginCard = document.getElementById("login-card");
    const adminPanel = document.getElementById("admin-panel");
    const loginForm = document.getElementById("login-form");
    const apiKeyInput = document.getElementById("api-key");
    const currentPromptTextarea = document.getElementById("current-prompt");
    const newPromptTextarea = document.getElementById("new-prompt");
    const refreshBtn = document.getElementById("refresh-btn");
    const copyBtn = document.getElementById("copy-btn");
    const updateBtn = document.getElementById("update-btn");
    const apiStatus = document.getElementById("api-status");
    const errorAlert = document.getElementById("error-alert");
    const successAlert = document.getElementById("success-alert");
    const errorMessage = document.getElementById("error-message");
    const successMessage = document.getElementById("success-message");
    const createUserForm = document.getElementById("create-user-form");
    const userCount = document.getElementById("user-count");
    
    // 导航相关元素
    const navLinks = document.querySelectorAll(".nav-link[data-section]");
    const sections = document.querySelectorAll(".admin-panel-section");

    // 模态框
    const deleteModal = new bootstrap.Modal(document.getElementById("deleteModal"));
    const editModal = new bootstrap.Modal(document.getElementById("editModal"));
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn");
    const confirmEditBtn = document.getElementById("confirm-edit-btn");
    const deleteUsername = document.getElementById("delete-username");
    
    // 临时存储当前操作的用户ID
    let currentUserId = null;

    // 初始检查API密钥（从localStorage或会话cookie）
    checkApiKey();

    // 登录表单提交
    if (loginForm) {
        loginForm.addEventListener("submit", function(e) {
            e.preventDefault();
            const apiKey = apiKeyInput.value.trim();
            if (apiKey) {
                // 存储在localStorage
                localStorage.setItem('admin_api_key', apiKey);
                showAdminPanel();
                loadInitialData();
            } else {
                showError("请输入API密钥");
            }
        });
    }

    // 退出按钮点击
    document.getElementById("logout-btn").addEventListener("click", function(e) {
        localStorage.removeItem('admin_api_key');
    });

    // 导航点击事件
    navLinks.forEach(link => {
        link.addEventListener("click", function(e) {
            e.preventDefault();
            const targetSectionId = this.getAttribute("data-section");
            
            // 移除所有导航链接的active类
            navLinks.forEach(navLink => navLink.classList.remove("active"));
            // 添加当前链接的active类
            this.classList.add("active");
            
            // 隐藏所有section
            sections.forEach(section => section.classList.remove("active"));
            
            // 显示目标section
            document.getElementById(targetSectionId).classList.add("active");
            
            // 根据需要加载特定部分的数据
            if (targetSectionId === "users-section") {
                loadUsers();
            } else if (targetSectionId === "dashboard-section") {
                loadDashboardData();
            }
        });
    });

    // 刷新提示词按钮点击
    if (refreshBtn) {
        refreshBtn.addEventListener("click", loadCurrentPrompt);
    }

    // 复制提示词按钮点击
    if (copyBtn) {
        copyBtn.addEventListener("click", function() {
            newPromptTextarea.value = currentPromptTextarea.value;
        });
    }

    // 更新提示词按钮点击
    if (updateBtn) {
        updateBtn.addEventListener("click", updatePrompt);
    }
    
    // 创建用户表单提交
    if (createUserForm) {
        createUserForm.addEventListener("submit", async function(e) {
            e.preventDefault();
            
            const userData = {
                username: document.getElementById("new-username").value,
                email: document.getElementById("new-email").value,
                password: document.getElementById("new-password").value,
                role: document.getElementById("new-role").value
            };
            
            try {
                const response = await fetch("/api/admin/users/create", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    credentials: "include",
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 显示成功消息
                    showSuccess("用户创建成功!");
                    
                    // 清空表单
                    createUserForm.reset();
                    
                    // 重新加载用户列表和仪表板数据
                    loadUsers();
                    loadDashboardData();
                } else {
                    showError(data.message || "创建用户失败");
                }
            } catch (error) {
                console.error("创建用户错误:", error);
                showError("创建用户时发生错误");
            }
        });
    }
    
    // 删除用户确认
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", async function() {
            if (!currentUserId) return;
            
            try {
                const response = await fetch(`/api/admin/users/${currentUserId}`, {
                    method: "DELETE",
                    credentials: "include"
                });
                
                const result = await response.json();
                
                // 关闭模态窗口
                deleteModal.hide();
                
                if (result.success) {
                    // 显示成功消息
                    showSuccess("用户已成功删除");
                    
                    // 重新加载用户列表和仪表板数据
                    loadUsers();
                    loadDashboardData();
                } else {
                    showError(result.message || "删除用户失败");
                }
            } catch (error) {
                console.error("删除用户错误:", error);
                deleteModal.hide();
                showError("删除用户时发生错误");
            }
        });
    }
    
    // 编辑用户确认
    if (confirmEditBtn) {
        confirmEditBtn.addEventListener("click", async function() {
            if (!currentUserId) return;
            
            const userData = {
                email: document.getElementById("edit-email").value,
                role: document.getElementById("edit-role").value
            };
            
            // 只有在填写了密码的情况下才包含密码字段
            const password = document.getElementById("edit-password").value;
            if (password) {
                userData.password = password;
            }
            
            try {
                const response = await fetch(`/api/admin/users/${currentUserId}/update`, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    credentials: "include",
                    body: JSON.stringify(userData)
                });
                
                const result = await response.json();
                
                // 关闭模态窗口
                editModal.hide();
                
                if (result.success) {
                    // 显示成功消息
                    showSuccess("用户信息已成功更新");
                    
                    // 重新加载用户列表
                    loadUsers();
                } else {
                    showError(result.message || "更新用户信息失败");
                }
            } catch (error) {
                console.error("更新用户错误:", error);
                editModal.hide();
                showError("更新用户信息时发生错误");
            }
        });
    }
    
    // 系统设置按钮事件
    document.getElementById("clear-cache-btn")?.addEventListener("click", function() {
        showSuccess("缓存已清除");
    });
    
    document.getElementById("backup-btn")?.addEventListener("click", function() {
        showSuccess("数据库备份已启动，完成后将通知您");
    });

    // 函数: 检查API密钥
    function checkApiKey() {
        const storedApiKey = localStorage.getItem('admin_api_key');
        if (storedApiKey) {
            showAdminPanel();
            loadInitialData();
        }
    }

    // 函数: 显示管理面板
    function showAdminPanel() {
        if (loginCard) loginCard.style.display = "none";
        if (adminPanel) adminPanel.style.display = "block";
    }

    // 函数: 加载初始数据
    function loadInitialData() {
        loadCurrentPrompt();
        loadDashboardData();
        loadUsers();
        loadPrompts();
        loadSettings();
    }
    
    // 函数: 加载仪表板数据
    async function loadDashboardData() {
        try {
            // 加载用户数量
            const response = await fetch("/api/admin/users", {
                credentials: "include"
            });
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success && data.data && data.data.users) {
                    if (userCount) {
                        userCount.textContent = data.data.users.length;
                    }
                }
            }
        } catch (error) {
            console.error("加载仪表板数据错误:", error);
        }
    }

    // 函数: 加载当前提示词
    async function loadCurrentPrompt() {
        try {
            const response = await fetch("/api/admin/get-prompt", {
                method: "GET",
                credentials: "include"
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data && data.data.prompt) {
                    currentPromptTextarea.value = data.data.prompt;
                } else {
                    showError("获取提示词失败: " + (data.message || "未知错误"));
                }
            } else {
                const errorData = await response.json();
                showError("获取提示词失败: " + (errorData.message || "API响应错误"));
            }
        } catch (error) {
            console.error("加载提示词错误:", error);
            showError("加载提示词时发生错误");
        }
    }

    // 函数: 更新提示词
    async function updatePrompt() {
        const newPrompt = newPromptTextarea.value.trim();
        if (!newPrompt) {
            showError("新提示词不能为空");
            return;
        }

        try {
            const response = await fetch("/api/admin/update-prompt", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                credentials: "include", 
                body: JSON.stringify({ new_prompt: newPrompt })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    showSuccess("提示词更新成功");
                    // 更新当前提示词显示
                    currentPromptTextarea.value = newPrompt;
                    // 清空新提示词输入框
                    newPromptTextarea.value = "";
                } else {
                    showError("更新提示词失败: " + (data.message || "未知错误"));
                }
            } else {
                const errorData = await response.json();
                showError("更新提示词失败: " + (errorData.message || "API响应错误"));
            }
        } catch (error) {
            console.error("更新提示词错误:", error);
            showError("更新提示词时发生错误");
        }
    }
    
    // 函数: 加载用户列表
    async function loadUsers() {
        const tableBody = document.getElementById("user-table-body");
        if (!tableBody) return;
        
        try {
            const response = await fetch("/api/admin/users", {
                credentials: "include"
            });
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success && data.data && data.data.users) {
                    renderUserTable(data.data.users);
                } else {
                    showError("获取用户列表数据格式错误");
                }
            } else {
                const error = await response.json();
                showError(error.message || "获取用户列表失败");
            }
        } catch (error) {
            console.error("加载用户列表错误:", error);
            showError("加载用户列表失败");
        }
    }
    
    // 函数: 渲染用户表格
    function renderUserTable(users) {
        const tableBody = document.getElementById("user-table-body");
        if (!tableBody) return;
        
        // 清空表格内容
        tableBody.innerHTML = "";
        
        if (users.length === 0) {
            const row = document.createElement("tr");
            row.innerHTML = `<td colspan="8" class="text-center">没有找到任何用户</td>`;
            tableBody.appendChild(row);
            return;
        }
        
        // 添加用户行
        users.forEach(user => {
            const row = document.createElement("tr");
            
            // 格式化日期
            const createdAt = new Date(user.created_at).toLocaleString();
            const lastLogin = user.last_login ? new Date(user.last_login).toLocaleString() : "从未登录";
            
            // 根据状态显示不同的标签
            const statusBadge = user.is_active 
                ? `<span class="badge bg-success">已启用</span>` 
                : `<span class="badge bg-danger">已禁用</span>`;
            
            // 操作按钮
            const toggleStatusBtn = user.is_active
                ? `<button class="btn btn-sm btn-warning toggle-status" data-id="${user.id}">禁用</button>`
                : `<button class="btn btn-sm btn-success toggle-status" data-id="${user.id}">启用</button>`;
            
            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${user.role === "admin" ? "管理员" : "普通用户"}</td>
                <td>${createdAt}</td>
                <td>${lastLogin}</td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-primary me-1 edit-user" data-id="${user.id}" data-username="${user.username}" data-email="${user.email}" data-role="${user.role}">编辑</button>
                    ${toggleStatusBtn}
                    <button class="btn btn-sm btn-danger ms-1 delete-user" data-id="${user.id}" data-username="${user.username}">删除</button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // 绑定删除按钮事件
        document.querySelectorAll(".delete-user").forEach(button => {
            button.addEventListener("click", function() {
                const userId = this.getAttribute("data-id");
                const username = this.getAttribute("data-username");
                
                // 设置当前操作的用户ID和用户名
                currentUserId = userId;
                deleteUsername.textContent = username;
                
                // 显示确认对话框
                deleteModal.show();
            });
        });
        
        // 绑定编辑按钮事件
        document.querySelectorAll(".edit-user").forEach(button => {
            button.addEventListener("click", function() {
                const userId = this.getAttribute("data-id");
                const username = this.getAttribute("data-username");
                const email = this.getAttribute("data-email");
                const role = this.getAttribute("data-role");
                
                // 设置当前操作的用户ID和表单值
                currentUserId = userId;
                document.getElementById("edit-username").value = username;
                document.getElementById("edit-email").value = email;
                document.getElementById("edit-password").value = "";
                document.getElementById("edit-role").value = role;
                
                // 显示编辑对话框
                editModal.show();
            });
        });
        
        // 绑定启用/禁用按钮事件
        document.querySelectorAll(".toggle-status").forEach(button => {
            button.addEventListener("click", async function() {
                const userId = this.getAttribute("data-id");
                
                try {
                    const response = await fetch(`/api/admin/users/${userId}/toggle-status`, {
                        method: "POST",
                        credentials: "include"
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showSuccess(result.message || "用户状态已更新");
                        loadUsers(); // 重新加载用户列表
                        loadDashboardData(); // 更新仪表板数据
                    } else {
                        showError(result.message || "更新用户状态失败");
                    }
                } catch (error) {
                    console.error("切换用户状态错误:", error);
                    showError("更新用户状态时发生错误");
                }
            });
        });
    }

    // 函数: 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.style.display = "block";
        
        // 5秒后自动隐藏
        setTimeout(() => {
            errorAlert.style.display = "none";
        }, 5000);
        
        // 隐藏成功消息
        successAlert.style.display = "none";
    }

    // 函数: 显示成功信息
    function showSuccess(message) {
        successMessage.textContent = message;
        successAlert.style.display = "block";
        
        // 5秒后自动隐藏
        setTimeout(() => {
            successAlert.style.display = "none";
        }, 5000);
        
        // 隐藏错误消息
        errorAlert.style.display = "none";
    }

    // 加载系统设置
    async function loadSettings() {
        try {
            // 获取所有系统设置
            const response = await fetch("/api/admin/settings", {
                method: "GET",
                credentials: "include"
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 更新统计数据
                document.getElementById("total-settings").textContent = data.data.settings.length;
                
                // 查找最后更新时间
                let lastUpdate = null;
                let apiKeySet = false;
                let apiBaseSet = false;
                
                // 清空设置表格
                const settingsTable = document.getElementById("settings-table");
                settingsTable.innerHTML = "";
                
                // 填充设置表格
                data.data.settings.forEach(setting => {
                    // 更新最后更新时间
                    if (!lastUpdate || new Date(setting.updated_at) > new Date(lastUpdate)) {
                        lastUpdate = setting.updated_at;
                    }
                    
                    // 检查API密钥是否已设置
                    if (setting.key === "openai_api_key" && setting.value) {
                        apiKeySet = true;
                        // 预填充API密钥输入框（显示掩码值）
                        document.getElementById("openai-api-key").placeholder = setting.value;
                    }
                    
                    // 检查API基础URL是否已设置
                    if (setting.key === "openai_api_base" && setting.value) {
                        apiBaseSet = true;
                        // 预填充API基础URL输入框
                        document.getElementById("openai-api-base").value = setting.value;
                    }
                    
                    // 创建表格行
                    const row = document.createElement("tr");
                    
                    // 不在表格中显示API密钥和API基础URL设置，因为它们有专门的表单
                    if (setting.key !== "openai_api_key" && setting.key !== "openai_api_base") {
                        row.innerHTML = `
                            <td>${setting.key}</td>
                            <td>${setting.value}</td>
                            <td>${setting.description || '-'}</td>
                            <td>${formatDate(setting.updated_at)}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editSetting('${setting.key}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        `;
                        settingsTable.appendChild(row);
                    }
                });
                
                // 更新API密钥状态
                document.getElementById("api-key-status").textContent = apiKeySet ? "已设置" : "未设置";
                document.getElementById("api-key-status").style.color = apiKeySet ? "white" : "yellow";
                
                // 如果API基础URL未设置，设置为默认值
                if (!apiBaseSet) {
                    document.getElementById("openai-api-base").value = "https://api.openai.com/v1";
                }
                
                // 更新最后更新时间
                if (lastUpdate) {
                    document.getElementById("last-settings-update").textContent = formatDate(lastUpdate);
                }
                
                // 获取特定的API密钥设置（用于测试按钮状态）
                const apiKeyResponse = await fetch("/api/admin/settings/openai_api_key", {
                    method: "GET",
                    credentials: "include"
                });
                
                const apiKeyData = await apiKeyResponse.json();
                
                // 如果API密钥已设置，启用测试按钮
                document.getElementById("test-api-key").disabled = !apiKeyData.success;
                
            } else {
                showError(data.message || "加载系统设置失败");
            }
        } catch (error) {
            console.error("加载系统设置错误:", error);
            showError("加载系统设置时发生错误");
        }
    }

    // 编辑系统设置
    function editSetting(key) {
        // 创建编辑模态框
        const modalHtml = `
            <div class="modal fade" id="editSettingModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑设置: ${key}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="edit-setting-form">
                                <div class="mb-3">
                                    <label class="form-label">设置值</label>
                                    <input type="text" class="form-control" id="edit-setting-value" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">描述</label>
                                    <textarea class="form-control" id="edit-setting-description" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="save-setting-btn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加模态框到DOM
        const modalContainer = document.createElement("div");
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer);
        
        // 获取设置详情
        fetch(`/api/admin/settings/${key}`, {
            method: "GET",
            credentials: "include"
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById("edit-setting-value").value = data.data.raw_value;
                // 可能需要额外请求获取描述
            }
        })
        .catch(error => {
            console.error("获取设置详情错误:", error);
        });
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById("editSettingModal"));
        modal.show();
        
        // 保存按钮点击事件
        document.getElementById("save-setting-btn").addEventListener("click", async () => {
            const value = document.getElementById("edit-setting-value").value;
            const description = document.getElementById("edit-setting-description").value;
            
            try {
                const response = await fetch("/api/admin/settings/update", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    credentials: "include",
                    body: JSON.stringify({
                        key: key,
                        value: value,
                        description: description
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess("设置已更新");
                    modal.hide();
                    // 重新加载设置
                    loadSettings();
                    
                    // 移除模态框
                    setTimeout(() => {
                        document.body.removeChild(modalContainer);
                    }, 500);
                } else {
                    showError(result.message || "更新设置失败");
                }
            } catch (error) {
                console.error("更新设置错误:", error);
                showError("更新设置时发生错误");
            }
        });
    }

    // 格式化日期
    function formatDate(dateString) {
        if (!dateString) return "-";
        const date = new Date(dateString);
        return date.toLocaleString("zh-CN");
    }

    // 添加API密钥表单提交事件
    document.getElementById("api-key-form")?.addEventListener("submit", async function(e) {
        e.preventDefault();
        
        const apiKey = document.getElementById("openai-api-key").value;
        
        try {
            const response = await fetch("/api/admin/settings/update", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                credentials: "include",
                body: JSON.stringify({
                    key: "openai_api_key",
                    value: apiKey,
                    description: "OpenAI API密钥，用于图像处理服务"
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showSuccess("API密钥已更新");
                // 清空输入框
                document.getElementById("openai-api-key").value = "";
                // 重新加载设置
                loadSettings();
            } else {
                showError(result.message || "更新API密钥失败");
            }
        } catch (error) {
            console.error("更新API密钥错误:", error);
            showError("更新API密钥时发生错误");
        }
    });

    // 添加API密钥显示切换
    document.getElementById("toggle-api-key")?.addEventListener("click", function() {
        const apiKeyInput = document.getElementById("openai-api-key");
        const icon = this.querySelector("i");
        
        if (apiKeyInput.type === "password") {
            apiKeyInput.type = "text";
            icon.classList.remove("fa-eye");
            icon.classList.add("fa-eye-slash");
        } else {
            apiKeyInput.type = "password";
            icon.classList.remove("fa-eye-slash");
            icon.classList.add("fa-eye");
        }
    });

    // 添加测试API密钥功能
    document.getElementById("test-api-key")?.addEventListener("click", async function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>测试中...';
        
        try {
            // 这里可以调用一个专门的API测试接口
            // 暂时模拟测试过程
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            showSuccess("API密钥测试成功");
        } catch (error) {
            console.error("测试API密钥错误:", error);
            showError("测试API密钥时发生错误");
        } finally {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-vial me-2"></i>测试API密钥';
        }
    });

    // 添加API基础URL表单提交事件
    document.getElementById("api-base-form")?.addEventListener("submit", async function(e) {
        e.preventDefault();
        
        const apiBase = document.getElementById("openai-api-base").value;
        
        try {
            const response = await fetch("/api/admin/settings/update", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                credentials: "include",
                body: JSON.stringify({
                    key: "openai_api_base",
                    value: apiBase,
                    description: "OpenAI API基础URL，用于图像处理服务"
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showSuccess("API基础URL已更新");
                // 重新加载设置
                loadSettings();
            } else {
                showError(result.message || "更新API基础URL失败");
            }
        } catch (error) {
            console.error("更新API基础URL错误:", error);
            showError("更新API基础URL时发生错误");
        }
    });

    // 添加重置API基础URL按钮事件
    document.getElementById("reset-api-base")?.addEventListener("click", async function() {
        // 设置为默认值
        document.getElementById("openai-api-base").value = "https://api.openai.com/v1";
        
        try {
            const response = await fetch("/api/admin/settings/update", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                credentials: "include",
                body: JSON.stringify({
                    key: "openai_api_base",
                    value: "https://api.openai.com/v1",
                    description: "OpenAI API基础URL，用于图像处理服务"
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showSuccess("API基础URL已重置为默认值");
                // 重新加载设置
                loadSettings();
            } else {
                showError(result.message || "重置API基础URL失败");
            }
        } catch (error) {
            console.error("重置API基础URL错误:", error);
            showError("重置API基础URL时发生错误");
        }
    });
}); 