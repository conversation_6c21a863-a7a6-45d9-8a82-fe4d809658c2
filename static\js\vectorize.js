document.addEventListener('DOMContentLoaded', function() {
    // 元素获取
    const uploadArea = document.getElementById('vector-upload-area');
    const uploadPrompt = document.getElementById('vector-upload-prompt');
    const previewArea = document.getElementById('vector-preview-area');
    const previewImage = document.getElementById('vector-preview-image');
    const fileInput = document.getElementById('vector-file-input');
    const removeBtn = document.getElementById('vector-remove-btn');
    const processBtn = document.getElementById('vector-process-btn');
    const processingCard = document.getElementById('vector-processing-card');
    const resultCard = document.getElementById('vector-result-card');
    const originalImage = document.getElementById('vector-original-image');
    const resultImage = document.getElementById('vector-result-image');
    const downloadSvgBtn = document.getElementById('vector-download-svg-btn');
    const downloadPngBtn = document.getElementById('vector-download-png-btn');
    const newUploadBtn = document.getElementById('vector-new-upload-btn');
    const errorAlert = document.getElementById('vector-error-alert');
    const errorMessage = document.getElementById('vector-error-message');
    
    let uploadedFile = null;
    
    // 拖放上传功能
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        uploadArea.classList.add('dragover');
    }

    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length) {
            handleFiles(files);
        }
    }

    // 文件选择功能
    fileInput.addEventListener('change', function() {
        if (this.files.length) {
            handleFiles(this.files);
        }
    });

    // 移除图片按钮
    removeBtn.addEventListener('click', function() {
        resetUploadForm();
    });

    // 处理按钮
    processBtn.addEventListener('click', function() {
        if (uploadedFile) {
            // 提醒用户会扣除积分
            if (confirm('将扣除5积分生成矢量图，确定继续吗？')) {
                processVectorImage(uploadedFile);
            }
        }
    });

    // 上传新图片按钮
    newUploadBtn.addEventListener('click', function() {
        resetAll();
    });

    // 处理文件
    function handleFiles(files) {
        const file = files[0]; // 只处理第一个文件
        
        // 检查文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            showError('不支持的文件类型，请上传JPG, PNG或GIF图片。');
            return;
        }
        
        // 检查文件大小（10MB限制）
        if (file.size > 10 * 1024 * 1024) {
            showError('文件过大，请上传小于10MB的图片。');
            return;
        }

        // 预览图片
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            uploadPrompt.style.display = 'none';
            previewArea.style.display = 'block';
            processBtn.disabled = false;
        };
        reader.readAsDataURL(file);
        
        // 保存文件
        uploadedFile = file;
        
        // 隐藏错误信息
        hideError();
    }

    // 处理矢量图
    function processVectorImage(file) {
        // 显示处理中状态
        uploadArea.style.display = 'none';
        processingCard.style.display = 'block';
        processBtn.disabled = true;
        
        // 获取选中的质量
        const qualityValue = document.querySelector('input[name="vector-quality"]:checked').value;
        
        // 创建表单数据
        const formData = new FormData();
        formData.append('file', file);
        formData.append('quality', qualityValue);

        // 发送API请求处理图像
        fetch('/api/vectorize', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应错误: ' + response.status);
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                // 隐藏处理中状态
                processingCard.style.display = 'none';
                
                // 显示结果
                resultCard.style.display = 'block';
                
                // 显示原图
                originalImage.src = `/uploads/${result.data.original_file}`;
                
                // 显示结果图
                resultImage.src = `/vector_outputs/${result.data.preview_file}`;
                
                // 设置下载链接
                downloadSvgBtn.href = `/api/download/vector/${result.data.svg_file}`;
                downloadSvgBtn.download = result.data.svg_file;
                
                downloadPngBtn.href = `/api/download/vector/${result.data.png_file}`;
                downloadPngBtn.download = result.data.png_file;
            } else {
                throw new Error(result.message || '处理失败');
            }
        })
        .catch(error => {
            processingCard.style.display = 'none';
            uploadArea.style.display = 'block';
            showError('生成矢量图时出错: ' + error.message);
            processBtn.disabled = false;
        });
    }

    // 重置上传表单
    function resetUploadForm() {
        previewArea.style.display = 'none';
        uploadPrompt.style.display = 'block';
        previewImage.src = '#';
        processBtn.disabled = true;
        
        // 清除文件
        fileInput.value = '';
        uploadedFile = null;
    }

    // 重置所有状态
    function resetAll() {
        resetUploadForm();
        resultCard.style.display = 'none';
        uploadArea.style.display = 'block';
        hideError();
    }

    // 显示错误信息
    function showError(message) {
        errorMessage.innerHTML = message;
        errorAlert.style.display = 'block';
    }

    // 隐藏错误信息
    function hideError() {
        errorAlert.style.display = 'none';
    }
}); 