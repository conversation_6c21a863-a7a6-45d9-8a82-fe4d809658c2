FROM python:3.10-slim

WORKDIR /app

# 复制依赖文件并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p uploads outputs prompts data
RUN chmod 777 uploads outputs prompts data

# 设置环境变量
ENV HOST=0.0.0.0
ENV PORT=8000
ENV DEBUG=False
ENV DB_PATH=/app/data/frmaster.db

# 暴露端口
EXPOSE 8000

# 运行应用
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"] 