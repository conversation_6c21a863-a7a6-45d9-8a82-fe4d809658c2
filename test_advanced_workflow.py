#!/usr/bin/env python3
"""
测试Vector Magic高级工作流程
验证新的自动化流程是否正常工作
"""

import os
import time
import logging
from vectorize_service import VectorizeService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_vector_magic_workflow():
    """测试Vector Magic工作流程"""
    print("=" * 60)
    print("Vector Magic 高级工作流程测试")
    print("=" * 60)
    
    # 检查Vector Magic配置
    print("\n1. 检查Vector Magic配置...")
    is_installed, message = VectorizeService.check_installation()
    if not is_installed:
        print(f"❌ {message}")
        return False
    
    print(f"✅ {message}")
    
    # 检查测试图片
    print("\n2. 准备测试图片...")
    test_image_path = "test_image.png"
    
    if not os.path.exists(test_image_path):
        print("创建测试图片...")
        create_test_image(test_image_path)
    
    if os.path.exists(test_image_path):
        print(f"✅ 测试图片已准备: {test_image_path}")
    else:
        print("❌ 无法创建测试图片")
        return False
    
    # 询问是否继续
    print("\n3. 准备开始测试...")
    print("⚠️  警告：此测试将启动Vector Magic并执行自动化操作")
    print("请确保：")
    print("- Vector Magic已正确安装")
    print("- 没有其他重要程序在运行")
    print("- 您已保存所有重要工作")
    
    choice = input("\n是否继续测试? (y/n): ").lower().strip()
    if choice != 'y':
        print("测试已取消")
        return False
    
    # 执行测试
    print("\n4. 开始执行Vector Magic工作流程...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 调用高级工作流程
        result, error = VectorizeService.vectorize_image(test_image_path, quality="high")
        
        if error:
            print(f"❌ 工作流程失败: {error}")
            return False
        
        if result:
            print("✅ 工作流程执行成功!")
            print(f"生成的文件:")
            for key, value in result.items():
                if value:
                    print(f"  - {key}: {value}")
            
            # 检查文件是否真的存在
            print("\n5. 验证生成的文件...")
            vector_output_dir = "vector_outputs"
            all_files_exist = True
            
            for key, filename in result.items():
                if filename:
                    file_path = os.path.join(vector_output_dir, filename)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"  ✅ {filename} ({file_size} bytes)")
                    else:
                        print(f"  ❌ {filename} (文件不存在)")
                        all_files_exist = False
            
            if all_files_exist:
                print("\n🎉 所有测试通过！Vector Magic高级工作流程正常工作")
                return True
            else:
                print("\n⚠️  部分文件缺失，请检查工作流程")
                return False
        else:
            print("❌ 工作流程返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        logger.exception("测试异常详情")
        return False

def create_test_image(image_path):
    """创建测试图片"""
    try:
        from PIL import Image, ImageDraw
        
        # 创建一个简单的测试图片
        width, height = 200, 200
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # 绘制一些简单的图形
        draw.rectangle([50, 50, 150, 150], fill='blue', outline='black', width=2)
        draw.ellipse([75, 75, 125, 125], fill='red')
        
        # 保存图片
        image.save(image_path, 'PNG')
        print(f"✅ 测试图片已创建: {image_path}")
        
    except ImportError:
        print("⚠️  PIL库未安装，尝试创建简单的测试文件")
        # 创建一个简单的文本文件作为测试
        with open(image_path, 'w') as f:
            f.write("Test image placeholder")
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")

def test_individual_components():
    """测试各个组件"""
    print("\n" + "=" * 60)
    print("组件测试")
    print("=" * 60)
    
    # 测试窗口检测
    print("\n1. 测试窗口检测...")
    try:
        window = VectorizeService._get_vector_magic_window()
        if window:
            print(f"✅ 找到Vector Magic窗口: {window}")
        else:
            print("ℹ️  当前没有运行的Vector Magic窗口")
    except Exception as e:
        print(f"❌ 窗口检测失败: {e}")
    
    # 测试按钮检测
    print("\n2. 测试按钮检测...")
    try:
        # 检查按钮图片是否存在
        button_dir = "button_images"
        if os.path.exists(button_dir):
            button_files = [f for f in os.listdir(button_dir) if f.endswith('.png')]
            if button_files:
                print(f"✅ 找到 {len(button_files)} 个按钮图片:")
                for btn in button_files:
                    print(f"  - {btn}")
            else:
                print("ℹ️  button_images目录为空")
        else:
            print("ℹ️  button_images目录不存在")
            print("建议运行 button_capture_tool.py 来截取按钮图片")
    except Exception as e:
        print(f"❌ 按钮检测测试失败: {e}")
    
    # 测试文件检查
    print("\n3. 测试文件检查...")
    try:
        generated_files = VectorizeService._check_generated_files()
        if generated_files:
            print(f"✅ 找到 {len(generated_files)} 个最近生成的文件:")
            for file in generated_files:
                print(f"  - {file}")
        else:
            print("ℹ️  没有找到最近生成的矢量图文件")
    except Exception as e:
        print(f"❌ 文件检查测试失败: {e}")

def main():
    """主函数"""
    print("Vector Magic 高级工作流程测试工具")
    print("此工具用于测试新的自动化工作流程")
    
    # 选择测试类型
    print("\n请选择测试类型:")
    print("1. 完整工作流程测试（推荐）")
    print("2. 组件测试")
    print("3. 两者都测试")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == '1':
        success = test_vector_magic_workflow()
    elif choice == '2':
        test_individual_components()
        success = True
    elif choice == '3':
        test_individual_components()
        success = test_vector_magic_workflow()
    else:
        print("无效选择")
        return
    
    # 总结
    print("\n" + "=" * 60)
    if choice in ['1', '3']:
        if success:
            print("🎉 测试完成！工作流程正常")
            print("\n建议:")
            print("- 可以开始使用矢量图生成功能")
            print("- 如需调优，可使用 real_time_detection.py 工具")
        else:
            print("⚠️  测试发现问题")
            print("\n建议:")
            print("- 检查Vector Magic安装和配置")
            print("- 运行 button_capture_tool.py 截取按钮图片")
            print("- 查看日志了解详细错误信息")
    else:
        print("组件测试完成")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
