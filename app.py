import os
import logging
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, BackgroundTasks, Header, Request, Form, Response
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn
from pydantic import BaseModel
from typing import Optional, List
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from datetime import datetime, timedelta
from openai import OpenAI
import uuid

import config
from file_handler import file_handler
from gpt_service import gpt_service
from prompt_manager import prompt_manager
from models import User, UserInfo, UserRole, UserDB
from auth import authenticate_user, get_current_active_user, get_admin_user, user_db, create_session, delete_session, SESSION_ID
from database import db
import os.path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入矢量图处理服务
try:
    from vectorize_service import vectorize_service, HAS_WIN32, VectorizeService
    VECTOR_ENABLED = True
    logger.info("矢量图处理服务已成功加载")
except ImportError as e:
    logger.warning(f"矢量图处理服务加载失败，某些功能将不可用: {str(e)}")
    VECTOR_ENABLED = False
    HAS_WIN32 = False
    VectorizeService = None

# 检查是否需要从JSON迁移到SQLite
def check_and_migrate_data():
    """检查并迁移数据"""
    json_file = "users.json"
    db_file = "frmaster.db"
    
    # 如果JSON文件存在且不是备份文件(.bak)，执行迁移
    if os.path.exists(json_file) and not json_file.endswith('.bak'):
        from migrate_to_sqlite import migrate_users_from_json_to_sqlite
        logger.info("检测到users.json文件，开始执行数据迁移...")
        migrate_users_from_json_to_sqlite()
        logger.info("数据迁移完成")

# 创建FastAPI实例
app = FastAPI(
    title="FRMaster API",
    description="地毯图案提取Web服务",
    version="1.0.0",
)

# 应用启动时执行数据迁移
@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    logger.info("应用启动...")
    # 执行数据迁移
    check_and_migrate_data()
    # 创建管理员账户（如果不存在）
    user_db.create_admin_if_not_exists()
    # 创建矢量图输出目录
    os.makedirs("vector_outputs", exist_ok=True)
    logger.info("初始化完成")

# 应用关闭时关闭数据库连接
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的事件处理"""
    logger.info("应用关闭，清理资源...")
    db.close_connection()
    logger.info("资源清理完成")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")
app.mount("/vector_outputs", StaticFiles(directory="vector_outputs"), name="vector_outputs")

# 设置模板目录
templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))

# 数据模型
class PromptUpdateModel(BaseModel):
    """提示词更新请求模型"""
    new_prompt: str
    prompt_id: Optional[int] = None
    prompt_name: Optional[str] = None
    new_name: Optional[str] = None
    set_default: Optional[bool] = False

class PromptCreateModel(BaseModel):
    """提示词创建请求模型"""
    name: str
    content: str
    is_default: Optional[bool] = False

class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool
    message: str
    data: Optional[dict] = None

class LoginForm(BaseModel):
    """登录表单模型"""
    username: str
    password: str

# 添加用户创建模型
class UserCreateModel(BaseModel):
    """用户创建请求模型"""
    username: str
    email: str
    password: str
    role: str = UserRole.USER

# 安全依赖
security = HTTPBearer(auto_error=False)

def verify_admin_token(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """验证管理员Token"""
    if not credentials:
        raise HTTPException(status_code=401, detail="未提供认证凭据")
    
    expected_token = config.ADMIN_TOKEN
    if not expected_token:
        raise HTTPException(status_code=500, detail="服务器未配置管理员Token")
    
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=403, detail="无效的Token")
    
    return True

def clean_temp_files_task():
    """清理临时文件的后台任务"""
    file_handler.clean_old_files(days=1)

# 路由定义 - 前端页面
@app.get("/")
async def index_page(request: Request):
    """主页面 - 重定向到登录页面"""
    return RedirectResponse(url="/login")

@app.get("/login")
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/logout")
async def logout(response: Response, request: Request):
    """用户登出，清除会话"""
    session_id = request.cookies.get(SESSION_ID)
    if session_id:
        delete_session(response, session_id)
    return RedirectResponse(url="/login")

@app.get("/dashboard")
async def dashboard_page(request: Request, current_user: User = Depends(get_current_active_user)):
    """用户仪表盘页面 - 需要登录"""
    return templates.TemplateResponse("dashboard.html", {"request": request, "user": current_user})

@app.get("/admin")
async def admin_page(request: Request, current_user: User = Depends(get_admin_user)):
    """管理员页面 - 需要管理员权限"""
    return templates.TemplateResponse("admin.html", {"request": request, "user": current_user})

@app.get("/user-management")
async def user_management_page(request: Request, current_user: User = Depends(get_admin_user)):
    """用户管理页面 - 需要管理员权限"""
    return templates.TemplateResponse("user_management.html", {"request": request, "user": current_user})

@app.get("/vectorize")
async def vectorize_page(request: Request, current_user: User = Depends(get_current_active_user)):
    """矢量图生成页面 - 需要登录"""
    # 添加向模板传递VECTOR_ENABLED和HAS_WIN32状态
    return templates.TemplateResponse("vectorize.html", {
        "request": request, 
        "user": current_user,
        "vector_enabled": VECTOR_ENABLED,
        "has_win32": HAS_WIN32
    })

# 路由定义 - API
@app.get("/api/health", response_model=ApiResponse)
async def health_check():
    """健康检查接口"""
    return ApiResponse(success=True, message="FRMaster API服务正常运行")

# 用户认证API
@app.post("/api/auth/login", response_model=ApiResponse)
async def login(response: Response, form_data: LoginForm):
    """用户登录"""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        return ApiResponse(success=False, message="用户名或密码错误")
    if user.expiry_date and user.expiry_date < datetime.now():
        return ApiResponse(success=False, message="账号已过期")
    # 管理员不受积分限制
    if user.points <= 0 and user.role != UserRole.ADMIN:
        return ApiResponse(success=False, message="积分不足，请充值")
    # 创建会话
    create_session(response, user.username)
    # 更新最后登录时间
    user_db.update_last_login(user.username)
    return ApiResponse(
        success=True,
        message="登录成功",
        data={"username": user.username, "role": user.role, "points": user.points, "expiry_date": user.expiry_date}
    )

@app.get("/api/users/me", response_model=ApiResponse)
async def get_user_me(request: Request, current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return ApiResponse(
        success=True,
        message="获取用户信息成功",
        data={
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "role": current_user.role,
            "created_at": current_user.created_at,
            "last_login": current_user.last_login,
            "is_active": current_user.is_active,
            "points": current_user.points,
            "expiry_date": current_user.expiry_date
        }
    )

# 用户管理API（仅管理员）
@app.get("/api/admin/users", response_model=ApiResponse)
async def list_all_users(request: Request, current_user: User = Depends(get_admin_user)):
    """获取所有用户列表（仅管理员）"""
    users = user_db.list_users()
    # 获取所有用户的API调用统计
    api_calls_stats = {}
    api_calls = db.get_user_api_calls_count()
    if api_calls:
        for call in api_calls:
            api_calls_stats[call['user_id']] = {
                'total_calls': call['total_calls'],
                'success_calls': call['success_calls'],
                'error_calls': call['error_calls']
            }
    user_list = [{
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role,
        "created_at": user.created_at,
        "last_login": user.last_login,
        "is_active": user.is_active,
        "points": user.points,
        "expiry_date": user.expiry_date,
        "api_calls": api_calls_stats.get(user.id, {'total_calls': 0, 'success_calls': 0, 'error_calls': 0})
    } for user in users]
    return ApiResponse(
        success=True,
        message="获取用户列表成功",
        data={"users": user_list}
    )

# 添加用户API调用统计接口
@app.get("/api/admin/users/{user_id}/api-stats", response_model=ApiResponse)
async def get_user_api_stats(user_id: str, current_user: User = Depends(get_admin_user)):
    """获取特定用户的API调用统计（仅管理员）"""
    try:
        # 检查用户是否存在
        user = user_db.get_user_by_id(user_id)
        if not user:
            return ApiResponse(success=False, message="用户不存在")
        
        # 获取用户API调用统计
        stats = db.get_user_api_calls_count(user_id)
        
        if stats:
            api_stats = {
                'total_calls': stats['total_calls'] or 0,
                'success_calls': stats['success_calls'] or 0,
                'error_calls': stats['error_calls'] or 0
            }
        else:
            api_stats = {'total_calls': 0, 'success_calls': 0, 'error_calls': 0}
        
        # 获取最近10次调用记录
        recent_calls = db.fetch_all("""
            SELECT id, api_type, created_at, status, prompt_id
            FROM user_api_calls
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        """, (user_id,))
        
        recent_calls_list = []
        if recent_calls:
            for call in recent_calls:
                recent_calls_list.append({
                    'id': call['id'],
                    'api_type': call['api_type'],
                    'created_at': call['created_at'],
                    'status': call['status'],
                    'prompt_id': call['prompt_id']
                })
        
        return ApiResponse(
            success=True,
            message="获取用户API调用统计成功",
            data={
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email
                },
                "api_stats": api_stats,
                "recent_calls": recent_calls_list
            }
        )
    
    except Exception as e:
        logger.error(f"获取用户API调用统计失败: {str(e)}")
        return ApiResponse(success=False, message=f"获取用户API调用统计失败: {str(e)}")

# 添加用户创建API
@app.post("/api/admin/users/create", response_model=ApiResponse)
async def create_user(user_data: UserCreateModel, current_user: User = Depends(get_admin_user)):
    """创建新用户（仅管理员）"""
    try:
        # 创建用户
        new_user = user_db.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            role=user_data.role
        )
        
        return ApiResponse(
            success=True,
            message="用户创建成功",
            data={
                "id": new_user.id,
                "username": new_user.username,
                "email": new_user.email,
                "role": new_user.role
            }
        )
    except ValueError as e:
        return ApiResponse(success=False, message=str(e))
    except Exception as e:
        logger.error(f"创建用户时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"创建用户时发生错误: {str(e)}")

@app.delete("/api/admin/users/{user_id}", response_model=ApiResponse)
async def delete_user(user_id: str, request: Request, current_user: User = Depends(get_admin_user)):
    """删除用户（仅管理员）"""
    # 检查是否自己删自己
    if user_id == current_user.id:
        return ApiResponse(success=False, message="不能删除当前登录的用户")
    
    # 删除用户
    if user_db.delete_user(user_id):
        return ApiResponse(success=True, message="用户已成功删除")
    else:
        return ApiResponse(success=False, message="用户不存在或删除失败")

# 添加用户更新API
@app.put("/api/admin/users/{user_id}/update", response_model=ApiResponse)
async def update_user(user_id: str, user_data: dict, current_user: User = Depends(get_admin_user)):
    """更新用户信息（仅管理员）"""
    try:
        # 获取用户
        user = user_db.get_user_by_id(user_id)
        if not user:
            return ApiResponse(success=False, message="用户不存在")
        
        # 更新用户信息
        if "email" in user_data:
            user.email = user_data["email"]
        
        if "role" in user_data:
            user.role = user_data["role"]
        
        # 如果提供了密码，则更新密码
        if "password" in user_data and user_data["password"]:
            from models import pwd_context
            user.hashed_password = pwd_context.hash(user_data["password"])
        
        # 更新积分
        if "points" in user_data:
            user.points = int(user_data["points"])
            
        # 更新过期时间
        if "expiry_date" in user_data and user_data["expiry_date"]:
            try:
                user.expiry_date = datetime.fromisoformat(user_data["expiry_date"])
            except ValueError:
                return ApiResponse(success=False, message="过期时间格式无效")
        
        # 保存用户信息
        if user_db.update_user(user):
            return ApiResponse(success=True, message="用户信息更新成功")
        else:
            return ApiResponse(success=False, message="更新用户信息失败")
    
    except Exception as e:
        logger.error(f"更新用户信息时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"更新用户信息时发生错误: {str(e)}")

# 添加用户状态切换API
@app.post("/api/admin/users/{user_id}/toggle-status", response_model=ApiResponse)
async def toggle_user_status(user_id: str, current_user: User = Depends(get_admin_user)):
    """切换用户状态（激活/禁用）"""
    try:
        # 获取用户
        user = user_db.get_user_by_id(user_id)
        if not user:
            return ApiResponse(success=False, message="用户不存在")
        
        # 不能禁用自己
        if user_id == current_user.id:
            return ApiResponse(success=False, message="不能修改当前登录用户的状态")
        
        # 切换状态
        user.is_active = not user.is_active
        status_text = "激活" if user.is_active else "禁用"
        
        # 保存用户信息
        if user_db.update_user(user):
            return ApiResponse(success=True, message=f"用户已{status_text}")
        else:
            return ApiResponse(success=False, message=f"更新用户状态失败")
    
    except Exception as e:
        logger.error(f"切换用户状态时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"切换用户状态时发生错误: {str(e)}")

@app.post("/api/upload", response_model=ApiResponse)
async def upload_image(background_tasks: BackgroundTasks, file: UploadFile = File(...), 
                     current_user: User = Depends(get_current_active_user)):
    """上传图片处理接口 - 需要用户登录"""
    try:
        # 保存上传的文件
        file_path, error = await file_handler.save_uploaded_file(file)
        if error:
            return ApiResponse(success=False, message=error)
        
        # 原始文件名
        original_filename = file.filename
        filename = os.path.basename(file_path)
        
        # 安排后台清理临时文件
        background_tasks.add_task(clean_temp_files_task)
        
        return ApiResponse(
            success=True,
            message="图片上传成功",
            data={
                "file_path": file_path,  # 添加file_path字段
                "original_filename": original_filename,
                "filename": filename
            }
        )
    
    except Exception as e:
        logger.error(f"处理上传图片时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"处理上传图片时发生错误: {str(e)}")

@app.get("/api/download/{filename}")
async def download_file(filename: str, current_user: User = Depends(get_current_active_user)):
    """下载处理结果接口 - 需要用户登录"""
    try:
        file_path = os.path.join(config.OUTPUT_FOLDER, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(path=file_path, filename=filename)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载文件时发生错误: {str(e)}")

@app.post("/api/admin/update-prompt", response_model=ApiResponse)
async def update_prompt(prompt_data: PromptUpdateModel, current_user: User = Depends(get_admin_user)):
    """更新提示词接口（需要管理员权限）"""
    try:
        if not prompt_data.new_prompt or prompt_data.new_prompt.strip() == "":
            return ApiResponse(success=False, message="提示词不能为空")
        
        # 更新提示词
        success, message = prompt_manager.update_prompt(
            prompt_data.new_prompt, 
            prompt_id=prompt_data.prompt_id,
            prompt_name=prompt_data.prompt_name, 
            new_name=prompt_data.new_name,
            set_default=prompt_data.set_default
        )
        
        if success:
            return ApiResponse(success=True, message=message)
        else:
            return ApiResponse(success=False, message=message)
    
    except Exception as e:
        logger.error(f"更新提示词时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"更新提示词时发生错误: {str(e)}")

@app.get("/api/admin/get-prompt", response_model=ApiResponse)
async def get_prompt(
    prompt_id: Optional[int] = None, 
    prompt_name: Optional[str] = None, 
    current_user: User = Depends(get_admin_user)
):
    """获取提示词接口（需要管理员权限）"""
    try:
        prompt_text = prompt_manager.get_prompt(prompt_id=prompt_id, prompt_name=prompt_name)
        if prompt_text:
            return ApiResponse(
                success=True,
                message="获取提示词成功",
                data={"prompt": prompt_text}
            )
        else:
            return ApiResponse(success=False, message="获取提示词失败")
    
    except Exception as e:
        logger.error(f"获取提示词时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取提示词时发生错误: {str(e)}")

@app.get("/api/admin/prompts", response_model=ApiResponse)
async def list_prompts(current_user: User = Depends(get_admin_user)):
    """获取所有提示词列表（需要管理员权限）"""
    try:
        prompts = prompt_manager.list_prompts()
        prompts_list = [
            {
                "id": row[0],
                "name": row[1],
                "content_preview": row[2][:100] + "..." if len(row[2]) > 100 else row[2],
                "is_default": bool(row[3]),
                "created_at": row[4],
                "updated_at": row[5]
            } for row in prompts
        ]
        
        # 计算统计数据
        stats = {
            "total_prompts": len(prompts),
            "default_prompt": next((p["name"] for p in prompts_list if p["is_default"]), None)
        }
        
        return ApiResponse(
            success=True,
            message="获取提示词列表成功",
            data={"prompts": prompts_list, "stats": stats}
        )
    
    except Exception as e:
        logger.error(f"获取提示词列表时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取提示词列表时发生错误: {str(e)}")

@app.post("/api/admin/prompts/create", response_model=ApiResponse)
async def create_prompt(prompt_data: PromptCreateModel, current_user: User = Depends(get_admin_user)):
    """创建新提示词（需要管理员权限）"""
    try:
        if not prompt_data.content or not prompt_data.name:
            return ApiResponse(success=False, message="提示词名称和内容不能为空")
        
        # 创建提示词
        success, message = prompt_manager.create_prompt(
            prompt_data.name, 
            prompt_data.content, 
            is_default=1 if prompt_data.is_default else 0
        )
        
        if success:
            return ApiResponse(success=True, message=message)
        else:
            return ApiResponse(success=False, message=message)
    
    except Exception as e:
        logger.error(f"创建提示词时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"创建提示词时发生错误: {str(e)}")

@app.delete("/api/admin/prompts/{prompt_id}", response_model=ApiResponse)
async def delete_prompt(prompt_id: int, current_user: User = Depends(get_admin_user)):
    """删除提示词（需要管理员权限）"""
    try:
        # 删除提示词
        success, message = prompt_manager.delete_prompt(prompt_id=prompt_id)
        
        if success:
            return ApiResponse(success=True, message=message)
        else:
            return ApiResponse(success=False, message=message)
    
    except Exception as e:
        logger.error(f"删除提示词时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"删除提示词时发生错误: {str(e)}")

@app.post("/api/admin/prompts/{prompt_id}/set-default", response_model=ApiResponse)
async def set_default_prompt(prompt_id: int, current_user: User = Depends(get_admin_user)):
    """设置默认提示词（需要管理员权限）"""
    try:
        # 设置默认提示词
        success, message = prompt_manager.set_default_prompt(prompt_id=prompt_id)
        
        if success:
            return ApiResponse(success=True, message=message)
        else:
            return ApiResponse(success=False, message=message)
    
    except Exception as e:
        logger.error(f"设置默认提示词时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"设置默认提示词时发生错误: {str(e)}")

# Vector Magic配置管理接口（需要管理员权限）
@app.get("/api/admin/vector-magic/status", response_model=ApiResponse)
async def get_vector_magic_status(current_user: User = Depends(get_admin_user)):
    """获取Vector Magic安装状态（需要管理员权限）"""
    try:
        if not VECTOR_ENABLED or not VectorizeService:
            return ApiResponse(success=False, message="矢量图服务未启用")

        is_installed, message = VectorizeService.check_installation()
        current_path = VectorizeService.get_current_path()

        return ApiResponse(
            success=is_installed,
            message=message,
            data={
                "current_path": current_path,
                "is_installed": is_installed,
                "has_win32": HAS_WIN32
            }
        )

    except Exception as e:
        logger.error(f"检查Vector Magic状态时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"检查Vector Magic状态时发生错误: {str(e)}")

@app.post("/api/admin/vector-magic/set-path", response_model=ApiResponse)
async def set_vector_magic_path(
    path_data: dict,
    current_user: User = Depends(get_admin_user)
):
    """设置Vector Magic可执行文件路径（需要管理员权限）"""
    try:
        if not VECTOR_ENABLED or not VectorizeService:
            return ApiResponse(success=False, message="矢量图服务未启用")

        new_path = path_data.get("path", "").strip()
        if not new_path:
            return ApiResponse(success=False, message="路径不能为空")

        success, message = VectorizeService.set_vector_magic_path(new_path)

        return ApiResponse(success=success, message=message)

    except Exception as e:
        logger.error(f"设置Vector Magic路径时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"设置Vector Magic路径时发生错误: {str(e)}")

@app.get("/api/admin/vector-magic/system-status", response_model=ApiResponse)
async def get_system_status(current_user: User = Depends(get_admin_user)):
    """获取完整的系统状态信息（需要管理员权限）"""
    try:
        if not VECTOR_ENABLED or not VectorizeService:
            return ApiResponse(success=False, message="矢量图服务未启用")

        status = VectorizeService.get_system_status()

        return ApiResponse(
            success=True,
            message="系统状态获取成功",
            data=status
        )

    except Exception as e:
        logger.error(f"获取系统状态时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取系统状态时发生错误: {str(e)}")

# 用户可访问的提示词API
@app.get("/api/prompts", response_model=ApiResponse)
async def get_prompt_list_for_users(current_user: User = Depends(get_current_active_user)):
    """获取提示词列表（仅提示词名称和ID，供用户选择）"""
    try:
        prompts = prompt_manager.list_prompts()
        prompts_list = [
            {
                "id": row[0],
                "name": row[1],
                "is_default": bool(row[3])
            } for row in prompts
        ]
        
        return ApiResponse(
            success=True,
            message="获取提示词列表成功",
            data={"prompts": prompts_list}
        )
    
    except Exception as e:
        logger.error(f"获取用户提示词列表时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取提示词列表时发生错误: {str(e)}")
    
# 密钥兑换积分和时间接口
class RedeemKeyModel(BaseModel):
    key: str
    username: str

@app.post("/api/redeem", response_model=ApiResponse)
async def redeem_key(data: RedeemKeyModel):
    """用户通过密钥兑换积分和时间"""
    user = user_db.get_user_by_username(data.username)
    if not user:
        return ApiResponse(success=False, message="用户不存在")
    # 查询密钥
    key_info = db.fetch_one("SELECT * FROM redeem_keys WHERE redeem_key = ? AND used = 0", (data.key,))
    if not key_info:
        return ApiResponse(success=False, message="密钥无效或已被使用")
    # 增加积分和有效期
    add_points = int(key_info["points"])
    add_days = int(key_info["days"])
    user.points += add_points
    if user.expiry_date and user.expiry_date > datetime.now():
        user.expiry_date = user.expiry_date + timedelta(days=add_days)
    else:
        user.expiry_date = datetime.now() + timedelta(days=add_days)
    user_db.update_user(user)
    # 标记密钥已用
    now = datetime.now().isoformat()
    db.execute_query("UPDATE redeem_keys SET used = 1, used_by = ?, used_at = ? WHERE redeem_key = ?", (user.username, now, data.key))
    return ApiResponse(success=True, message=f"兑换成功，积分+{add_points}，有效期+{add_days}天", data={"points": user.points, "expiry_date": user.expiry_date})

# 管理员添加密钥接口
class CreateKeyModel(BaseModel):
    redeem_key: str
    points: int
    days: int
    description: Optional[str] = None

@app.post("/api/admin/create-key", response_model=ApiResponse)
async def create_redeem_key(data: CreateKeyModel, current_user: User = Depends(get_admin_user)):
    """管理员创建兑换密钥"""
    # 检查密钥是否已存在
    exists = db.fetch_one("SELECT 1 FROM redeem_keys WHERE redeem_key = ?", (data.redeem_key,))
    if exists:
        return ApiResponse(success=False, message="密钥已存在")
    now = datetime.now().isoformat()
    db.execute_query(
        "INSERT INTO redeem_keys (redeem_key, points, days, description, used, created_at) VALUES (?, ?, ?, ?, 0, ?)",
        (data.redeem_key, data.points, data.days, data.description or '', now)
    )
    return ApiResponse(success=True, message="密钥创建成功")

# 图像处理接口和转矢量图接口，处理前校验积分和有效期并扣除积分
@app.post("/api/process", response_model=ApiResponse)
async def process_image(
    background_tasks: BackgroundTasks,
    prompt_id: Optional[int] = Form(None),
    prompt_name: Optional[str] = Form(None),
    file_path: str = Form(...),
    current_user: User = Depends(get_current_active_user)
):
    """处理图像接口，使用指定的提示词或默认提示词"""
    # 校验积分和有效期
    if current_user.expiry_date and current_user.expiry_date < datetime.now():
        return ApiResponse(success=False, message="账号已过期")
    # 管理员不受积分限制
    if current_user.points < 3 and current_user.role != UserRole.ADMIN:
        return ApiResponse(success=False, message="积分不足，生成图像需3积分")
    # 只有普通用户才扣除积分，并且在此处立即扣除
    if current_user.role != UserRole.ADMIN:
        current_user.points -= 3
        user_db.update_user(current_user)
        logger.info(f"用户 {current_user.username} 积分已扣除3点，剩余积分: {current_user.points}")
    
    # 在函数开始时立即记录API调用开始
    api_call_id = None
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            # 记录失败调用
            db.record_api_call(current_user.id, "process_image", "error", prompt_id)
            return ApiResponse(success=False, message="文件不存在")
    
        # 获取提示词
        prompt_text = prompt_manager.get_prompt(prompt_id=prompt_id, prompt_name=prompt_name)
        
        # 先记录调用开始，状态为pending
        api_call_data = {
            "user_id": current_user.id,
            "api_type": "process_image",
            "created_at": datetime.now().isoformat(),
            "status": "pending",
            "prompt_id": prompt_id
        }
        if prompt_id is None:
            del api_call_data["prompt_id"]
        
        # 插入记录并获取ID
        conn = db.get_connection()
        cursor = conn.cursor()
        placeholders = ', '.join(['?' for _ in api_call_data.values()])
        columns = ', '.join(api_call_data.keys())
        query = f"INSERT INTO user_api_calls ({columns}) VALUES ({placeholders})"
        cursor.execute(query, tuple(api_call_data.values()))
        conn.commit()
        api_call_id = cursor.lastrowid
        
        # 将图像处理任务添加到后台任务
        def process_image_task():
            try:
                # 直接调用同步处理方法，不再使用异步循环
                logger.info(f"开始处理图像: {file_path}")
                result_file, output = gpt_service.process_image(file_path, prompt_text)
                
                # 检查处理结果
                if result_file is None:
                    # 更新为失败状态
                    db.execute_query(
                        "UPDATE user_api_calls SET status = ? WHERE id = ?", 
                        ("error", api_call_id)
                    )
                    logger.error(f"图像处理失败: {output}")
                else:
                    # 更新为成功状态
                    db.execute_query(
                        "UPDATE user_api_calls SET status = ? WHERE id = ?", 
                        ("success", api_call_id)
                    )
                    
                    # 更新API调用记录，添加result_file字段
                    result_filename = os.path.basename(result_file)
                    db.execute_query(
                        "UPDATE user_api_calls SET result_file = ? WHERE id = ?",
                        (result_filename, api_call_id)
                    )
                    
                    logger.info(f"图像处理成功，生成文件: {result_file}")
            except Exception as e:
                # 确保异常也被记录
                try:
                    db.execute_query(
                        "UPDATE user_api_calls SET status = ? WHERE id = ?", 
                        ("error", api_call_id)
                    )
                except Exception as db_error:
                    logger.error(f"更新数据库状态失败: {str(db_error)}")
                    
                logger.error(f"图像处理任务异常: {str(e)}")
        
        # 添加后台任务
        background_tasks.add_task(process_image_task)
        background_tasks.add_task(clean_temp_files_task)
        
        # 立即返回响应，不等待处理完成
        return ApiResponse(
            success=True,
            message="图像处理请求已提交，正在后台处理",
            data={
                "api_call_id": api_call_id,
                "status": "pending",
                "result_file": os.path.basename(file_path).replace(".", "_output.")  # 添加预计的结果文件名
            }
        )
    
    except Exception as e:
        # 确保即使在最外层异常中也更新记录状态
        if api_call_id:
            db.execute_query(
                "UPDATE user_api_calls SET status = ? WHERE id = ?", 
                ("error", api_call_id)
            )
        else:
            # 如果尚未创建记录，则创建一个错误记录
            db.record_api_call(current_user.id, "process_image", "error", prompt_id)
        
        logger.error(f"处理图像请求时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"处理图像请求时发生错误: {str(e)}")

@app.get("/api/process/status/{api_call_id}", response_model=ApiResponse)
async def check_process_status(api_call_id: int, current_user: User = Depends(get_current_active_user)):
    """检查图像处理状态"""
    try:
        # 查询API调用记录
        query = """
        SELECT status, result_file FROM user_api_calls 
        WHERE id = ? AND user_id = ? 
        LIMIT 1
        """
        result = db.fetch_one(query, (api_call_id, current_user.id))
        
        if not result:
            return ApiResponse(
                success=False, 
                message="未找到对应的处理记录或权限不足"
            )
        
        status = result["status"]
        # 使用索引访问而不是get方法
        result_file = None
        if "result_file" in result.keys():
            result_file = result["result_file"]
        
        return ApiResponse(
            success=True,
            message="获取处理状态成功",
            data={
                "api_call_id": api_call_id,
                "status": status,
                "result_file": result_file
            }
        )
    
    except Exception as e:
        logger.error(f"检查处理状态时发生错误: {str(e)}")
        return ApiResponse(
            success=False,
            message=f"检查处理状态时发生错误: {str(e)}"
        )

# 添加系统设置模型
class SystemSettingUpdateModel(BaseModel):
    """系统设置更新请求模型"""
    key: str
    value: str
    description: Optional[str] = None

# 添加系统设置API路由
@app.get("/api/admin/settings", response_model=ApiResponse)
async def get_system_settings(current_user: User = Depends(get_admin_user)):
    """获取所有系统设置（仅管理员）"""
    try:
        settings = db.list_system_settings()
        settings_list = []
        
        for setting in settings:
            # 直接使用原始值，不进行脱敏处理
            settings_list.append({
                "key": setting['setting_key'],
                "value": setting['setting_value'],
                "description": setting['description'],
                "updated_at": setting['updated_at']
            })
        
        return ApiResponse(
            success=True,
            message="获取系统设置成功",
            data={"settings": settings_list}
        )
    except Exception as e:
        logger.error(f"获取系统设置时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取系统设置时发生错误: {str(e)}")

@app.post("/api/admin/settings/update", response_model=ApiResponse)
async def update_system_setting(setting_data: SystemSettingUpdateModel, current_user: User = Depends(get_admin_user)):
    """更新系统设置（仅管理员）"""
    try:
        success = db.update_system_setting(
            setting_data.key, 
            setting_data.value, 
            setting_data.description
        )
        
        if success:
            return ApiResponse(success=True, message=f"系统设置 {setting_data.key} 已更新")
        else:
            return ApiResponse(success=False, message="更新系统设置失败")
    except Exception as e:
        logger.error(f"更新系统设置时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"更新系统设置时发生错误: {str(e)}")

@app.get("/api/admin/settings/{key}", response_model=ApiResponse)
async def get_system_setting(key: str, current_user: User = Depends(get_admin_user)):
    """获取特定系统设置（仅管理员）"""
    try:
        value = db.get_system_setting(key)
        
        if value is not None:
            return ApiResponse(
                success=True,
                message=f"获取系统设置 {key} 成功",
                data={"key": key, "value": value}
            )
        else:
            return ApiResponse(success=False, message=f"系统设置 {key} 不存在")
    except Exception as e:
        logger.error(f"获取系统设置时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"获取系统设置时发生错误: {str(e)}")

@app.post("/api/admin/test-api-key", response_model=ApiResponse)
async def test_api_key(current_user: User = Depends(get_admin_user)):
    """测试OpenAI API密钥是否有效（仅管理员）"""
    try:
        # 从数据库获取API密钥
        api_key = db.get_system_setting("openai_api_key")
        if not api_key:
            return ApiResponse(
                success=False,
                message="未找到API密钥，请先设置API密钥"
            )
        
        # 获取API基础URL（如果已设置）
        api_base = db.get_system_setting("openai_api_base")
        
        # 创建OpenAI客户端
        if api_base:
            client = OpenAI(api_key=api_key, base_url=api_base)
        else:
            client = OpenAI(api_key=api_key)
        
        # 使用最简单的模型检查，尝试列出模型
        try:
            # 使用列出模型API，这是验证API密钥有效的最轻量方式
            response = client.models.list()
            
            # 如果没有引发异常，则API密钥有效
            return ApiResponse(
                success=True,
                message="API密钥验证成功",
                data={"valid": True}
            )
        except Exception as e:
            logger.error(f"API密钥测试失败: {str(e)}")
            return ApiResponse(
                success=False,
                message=f"API密钥验证失败: {str(e)}",
                data={"valid": False, "error": str(e)}
            )
    
    except Exception as e:
        logger.error(f"测试API密钥时发生错误: {str(e)}")
        return ApiResponse(
            success=False,
            message=f"测试API密钥时发生错误: {str(e)}"
        )

@app.get("/api/admin/redeem-keys", response_model=ApiResponse)
async def list_redeem_keys(current_user: User = Depends(get_admin_user)):
    """获取所有兑换密钥（仅管理员）"""
    try:
        keys = db.fetch_all("SELECT * FROM redeem_keys ORDER BY created_at DESC")
        
        keys_list = []
        for key in keys:
            keys_list.append({
                "redeem_key": key["redeem_key"],
                "points": key["points"],
                "days": key["days"],
                "description": key["description"],
                "used": bool(key["used"]),
                "used_by": key["used_by"] if "used_by" in key else None,
                "used_at": key["used_at"] if "used_at" in key else None,
                "created_at": key["created_at"]
            })
        
        return ApiResponse(
            success=True,
            message="获取兑换密钥列表成功",
            data={"keys": keys_list}
        )
    except Exception as e:
        logger.error(f"获取兑换密钥列表失败: {str(e)}")
        return ApiResponse(success=False, message=f"获取兑换密钥列表失败: {str(e)}")

@app.delete("/api/admin/delete-key/{redeem_key}", response_model=ApiResponse)
async def delete_redeem_key(redeem_key: str, current_user: User = Depends(get_admin_user)):
    """删除兑换密钥（仅管理员）"""
    try:
        # 检查密钥是否存在
        key = db.fetch_one("SELECT * FROM redeem_keys WHERE redeem_key = ?", (redeem_key,))
        if not key:
            return ApiResponse(success=False, message="密钥不存在")
        
        # 检查密钥是否已被使用
        if key["used"]:
            return ApiResponse(success=False, message="无法删除已使用的密钥")
        
        # 删除密钥
        result = db.execute("DELETE FROM redeem_keys WHERE redeem_key = ?", (redeem_key,))
        
        if result:
            return ApiResponse(success=True, message="密钥已成功删除")
        else:
            return ApiResponse(success=False, message="删除密钥失败")
    except Exception as e:
        logger.error(f"删除密钥失败: {str(e)}")
        return ApiResponse(success=False, message=f"删除密钥失败: {str(e)}")

# 矢量图生成API接口 - 需要登录
@app.post("/api/vectorize", response_model=ApiResponse)
async def vectorize_image(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    quality: str = Form("medium"),
    current_user: User = Depends(get_current_active_user)
):
    """矢量图生成API接口 - 需要登录"""
    # 检查矢量图服务是否可用
    if not VECTOR_ENABLED:
        return ApiResponse(success=False, message="矢量图生成服务不可用，请联系管理员")
        
    # 检查win32模块是否可用    
    if not HAS_WIN32:
        return ApiResponse(success=False, message="系统缺少必要的win32模块，无法执行矢量图转换")
        
    # 校验积分和有效期
    if current_user.expiry_date and current_user.expiry_date < datetime.now():
        return ApiResponse(success=False, message="账号已过期")
    # 管理员不受积分限制
    if current_user.points < 5 and current_user.role != UserRole.ADMIN:
        return ApiResponse(success=False, message="积分不足，生成矢量图需5积分")
    # 只有普通用户才扣除积分，并且在此处立即扣除
    if current_user.role != UserRole.ADMIN:
        current_user.points -= 5
        user_db.update_user(current_user)
        logger.info(f"用户 {current_user.username} 积分已扣除5点，剩余积分: {current_user.points}")
    
    try:
        # 保存上传的文件
        file_path, error = await file_handler.save_uploaded_file(file)
        if error:
            return ApiResponse(success=False, message=error)
        
        # 记录原始文件名
        original_filename = os.path.basename(file_path)
        
        # 定义处理函数
        def process_vector_task():
            try:
                # 调用Vector Magic处理图像
                result, error = vectorize_service.vectorize_image(file_path, quality=quality)
                if error:
                    logger.error(f"处理矢量图时出错: {error}")
                else:
                    # 添加原始文件名到结果
                    result["original_file"] = original_filename
                    logger.info(f"矢量图处理成功: {result}")
            except Exception as e:
                logger.exception(f"矢量图生成任务异常: {str(e)}")
        
        # 添加后台任务
        background_tasks.add_task(process_vector_task)
        background_tasks.add_task(clean_temp_files_task)
        
        # 返回原始文件名和预期的结果文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        base_filename = f"{timestamp}_{unique_id}"
        
        return ApiResponse(
            success=True,
            message="矢量图生成请求已提交，正在后台处理",
            data={
                "original_file": original_filename,
                "svg_file": f"{base_filename}.svg",
                "png_file": f"{base_filename}.png",
                "preview_file": f"{base_filename}_preview.png"
            }
        )
    
    except Exception as e:
        logger.error(f"处理矢量图请求时发生错误: {str(e)}")
        return ApiResponse(success=False, message=f"处理矢量图请求时发生错误: {str(e)}")

# 矢量图下载接口 - 需要用户登录
@app.get("/api/download/vector/{filename}")
async def download_vector_file(filename: str, current_user: User = Depends(get_current_active_user)):
    """下载矢量图结果接口 - 需要用户登录"""
    try:
        file_path = os.path.join("vector_outputs", filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(path=file_path, filename=filename)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载矢量图文件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载矢量图文件时发生错误: {str(e)}")

# 启动服务器（直接运行此文件时）
if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG
    ) 