<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 地毯图案提取系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
</head>
<body>
    <div class="container">
        <header class="my-4 text-center">
            <h1>地毯图案提取系统</h1>
            <p class="lead">用户登录</p>
        </header>
        
        <div class="row">
            <div class="col-md-6 col-lg-5 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">用户登录</h5>
                    </div>
                    <div class="card-body">
                        <!-- 登录表单 -->
                        <form id="login-form">
                            <!-- 用户名 -->
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            
                            <!-- 密码 -->
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">登录</button>
                            </div>
                            
                            <!-- 错误信息显示 -->
                            <div class="alert alert-danger mt-3" id="error-message" style="display: none;"></div>
                        </form>
                        
                        <!-- 密钥兑换区域 -->
                        <div id="redeem-key-section" class="mt-4" style="display: none;">
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> 积分不足，请充值
                            </div>
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">兑换密钥</h6>
                                </div>
                                <div class="card-body">
                                    <form id="redeem-key-form">
                                        <div class="mb-3">
                                            <label for="key-code" class="form-label">请输入兑换密钥</label>
                                            <input type="text" class="form-control" id="key-code" placeholder="输入密钥">
                                        </div>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-success">兑换</button>
                                        </div>
                                        <div class="alert alert-danger mt-3" id="redeem-error" style="display: none;"></div>
                                        <div class="alert alert-success mt-3" id="redeem-success" style="display: none;"></div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 地毯图案提取系统</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const errorMessage = document.getElementById('error-message');
            const redeemKeySection = document.getElementById('redeem-key-section');
            const redeemKeyForm = document.getElementById('redeem-key-form');
            const redeemError = document.getElementById('redeem-error');
            const redeemSuccess = document.getElementById('redeem-success');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // 隐藏错误信息
                errorMessage.style.display = 'none';
                
                try {
                    // 获取表单数据
                    const userData = {
                        username: document.getElementById('username').value,
                        password: document.getElementById('password').value
                    };
                    
                    // 发送登录请求
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(userData),
                        credentials: 'include' // 包含凭证（cookies）
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 登录成功，跳转到仪表盘页面
                        window.location.href = '/dashboard';
                    } else {
                        // 登录失败，显示错误信息
                        errorMessage.textContent = data.message || '登录失败，请检查用户名和密码';
                        errorMessage.style.display = 'block';
                        
                        // 如果是积分不足的错误，显示兑换密钥区域
                        if (data.message && data.message.includes('积分不足')) {
                            redeemKeySection.style.display = 'block';
                        }
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    errorMessage.textContent = '登录过程中发生错误，请稍后再试';
                    errorMessage.style.display = 'block';
                }
            });
            
            // 密钥兑换表单提交处理
            redeemKeyForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // 隐藏消息
                redeemError.style.display = 'none';
                redeemSuccess.style.display = 'none';
                
                const keyCode = document.getElementById('key-code').value.trim();
                if (!keyCode) {
                    redeemError.textContent = '请输入密钥';
                    redeemError.style.display = 'block';
                    return;
                }
                
                try {
                    const username = document.getElementById('username').value;
                    
                    // 发送兑换请求
                    const response = await fetch('/api/redeem', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            key: keyCode
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 直接使用后端返回的消息，它已经包含了积分和有效期信息
                        redeemSuccess.textContent = data.message;
                        redeemSuccess.style.display = 'block';
                        document.getElementById('key-code').value = '';
                        
                        // 3秒后尝试自动登录
                        setTimeout(() => {
                            loginForm.dispatchEvent(new Event('submit'));
                        }, 3000);
                    } else {
                        redeemError.textContent = data.message || '兑换失败';
                        redeemError.style.display = 'block';
                    }
                } catch (error) {
                    console.error('兑换错误:', error);
                    redeemError.textContent = '兑换过程中发生错误，请稍后再试';
                    redeemError.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html> 