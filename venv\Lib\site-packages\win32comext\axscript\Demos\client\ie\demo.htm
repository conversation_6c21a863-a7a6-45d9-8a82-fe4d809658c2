<HTML>
<HEAD>
<TITLE>Python AXScript Demos</TITLE>
</HEAD>


<SCRIPT LANGUAGE="Python">
def Window_OnLoad():
	pass
#	import win32traceutil
#	print "Frames are", ax.window.frames._print_details_()
#	print "Frame 0 href = ", ax.frames.Item(0).location.href

def Name_OnLoad():
	print "Frame loading"

</SCRIPT>

<FRAMESET FRAMEBORDER=1 COLS = "250, *">
	<FRAME SRC="demo_menu.htm">
	<FRAME SRC="demo_check.htm" NAME="Body">
</FRAMESET>


</HTML>

