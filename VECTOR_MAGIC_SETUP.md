# Vector Magic 配置指南

## 概述

本系统集成了Vector Magic软件，用于将位图自动转换为高质量的矢量图。本文档将指导您完成Vector Magic的安装和配置。

## 前提条件

1. **Vector Magic软件**: 需要安装Vector Magic Desktop Edition
2. **Windows系统**: 目前仅支持Windows系统
3. **Python依赖**: 需要安装以下Python包：
   - `pywin32` (win32api, win32gui等)
   - `pyautogui` (用于自动化操作)

## 安装步骤

### 1. 安装Vector Magic

1. 从Vector Magic官网下载Desktop Edition
2. 按照安装向导完成安装
3. 记录安装路径（通常在以下位置之一）：
   - `C:\Program Files (x86)\Vector Magic\vmde.exe`
   - `C:\Program Files\Vector Magic\vmde.exe`
   - `C:\Program Files (x86)\Vector Magic\VectorMagic.exe`
   - `C:\Program Files\Vector Magic\VectorMagic.exe`

### 2. 配置系统路径

#### 方法1: 通过管理员界面配置

1. 以管理员身份登录系统
2. 访问管理页面 `/admin`
3. 点击左侧菜单的"矢量图配置"
4. 在"路径配置"部分输入Vector Magic可执行文件的完整路径
5. 点击"设置路径"按钮
6. 点击"检查状态"确认配置成功

#### 方法2: 通过配置文件

1. 在项目根目录创建或编辑 `vector_magic_config.txt` 文件
2. 在文件中写入Vector Magic可执行文件的完整路径，例如：
   ```
   C:\Program Files (x86)\Vector Magic\vmde.exe
   ```

#### 方法3: 通过环境变量

设置环境变量 `VECTOR_MAGIC_PATH` 为Vector Magic可执行文件的完整路径。

### 3. 测试配置

运行测试脚本验证配置：

```bash
python test_vector_magic.py
```

如果需要交互式设置路径：

```bash
python test_vector_magic.py --setup
```

## 使用说明

### 基本使用

1. 登录系统后访问 `/vectorize` 页面
2. 上传要转换的位图文件（支持JPG、PNG等格式）
3. 选择质量级别：
   - **低质量**: 快速处理，适合简单图像
   - **中等质量**: 平衡质量和速度（推荐）
   - **高质量**: 最佳质量，处理时间较长
4. 点击"生成矢量图"按钮
5. 等待处理完成后下载结果文件

### 输出文件

系统会生成以下文件：
- **SVG文件**: 矢量图格式，可无限缩放
- **PNG文件**: 高质量位图格式
- **预览图**: 处理结果的预览图

## 故障排除

### 常见问题

1. **错误: "Vector Magic未安装或路径不正确"**
   - 检查Vector Magic是否正确安装
   - 验证配置的路径是否正确
   - 确保路径指向正确的可执行文件（通常是vmde.exe）

2. **错误: "系统缺少必要的win32模块"**
   - 安装pywin32: `pip install pywin32`

3. **错误: "无法找到Vector Magic窗口"**
   - 确保Vector Magic能够正常启动
   - 检查是否有其他程序阻止Vector Magic运行
   - 尝试手动启动Vector Magic测试

4. **处理超时或失败**
   - 检查图片文件是否损坏
   - 尝试使用较小的图片文件
   - 确保系统有足够的内存和磁盘空间

### 调试模式

启用详细日志记录：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 手动测试

您可以手动测试Vector Magic的自动化流程：

1. 手动启动Vector Magic
2. 加载一个测试图片
3. 观察界面布局和按钮位置
4. 根据需要调整自动化脚本中的坐标

## 高级配置

### 自定义工作流程

如果默认的自动化流程不适合您的Vector Magic版本，可以修改 `vectorize_service.py` 中的工作流程方法：

- `_execute_basic_workflow()`: 基本质量工作流程
- `_execute_medium_workflow()`: 中等质量工作流程  
- `_execute_advanced_workflow()`: 高质量工作流程

### 性能优化

1. **并发处理**: 系统使用后台任务处理，避免阻塞用户界面
2. **资源清理**: 自动清理临时文件和关闭Vector Magic进程
3. **超时控制**: 设置合理的超时时间避免长时间等待

## 安全注意事项

1. **管理员权限**: 只有管理员可以配置Vector Magic路径
2. **文件验证**: 系统会验证上传文件的类型和大小
3. **路径验证**: 系统会验证配置的路径是否为有效的可执行文件

## 支持的文件格式

### 输入格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- TIFF (.tiff, .tif)

### 输出格式
- SVG (矢量图)
- PNG (高质量位图)

## 许可证要求

请确保您拥有Vector Magic的有效许可证。本系统仅提供自动化接口，不包含Vector Magic软件本身。

## 技术支持

如果遇到问题，请：

1. 首先运行测试脚本诊断问题
2. 检查系统日志获取详细错误信息
3. 确认Vector Magic软件本身工作正常
4. 联系系统管理员获取帮助
