# 地毯图案提取Web服务

这是一个用于上传图片并使用ChatGPT-4 Vision API提取地毯图案的Web服务。该服务使用FastAPI框架构建，支持文件上传、处理和下载功能，并包含管理员接口用于动态更新提示词。

## 功能特点

- 使用FastAPI构建的RESTful API
- 支持图片上传和处理
- 集成OpenAI GPT-4 Vision API
- 加密存储提示词系统
- 管理员接口用于更新提示词
- 文件安全验证和临时文件清理
- Docker容器化支持

## 项目结构

```
frmaster_service/
├── app.py                 # 主应用文件
├── config.py             # 配置文件
├── prompt_manager.py     # 提示词管理模块
├── gpt_service.py        # GPT API调用服务
├── file_handler.py       # 文件处理模块
├── requirements.txt      # 依赖列表
├── .env                  # 环境变量
├── uploads/              # 上传目录
├── outputs/              # 输出目录
└── prompts/              # 提示词配置目录
```

## 安装与部署

### 本地开发环境

1. **克隆项目**
   ```
   git clone <repository-url>
   cd frmaster_service
   ```

2. **创建虚拟环境**
   ```
   python -m venv venv
   ```

3. **激活虚拟环境**
   - Windows:
     ```
     venv\Scripts\activate
     ```
   - Linux/Mac:
     ```
     source venv/bin/activate
     ```

4. **安装依赖**
   ```
   pip install -r requirements.txt
   ```

5. **配置环境变量**
   - 复制.env.example为.env
   - 编辑.env，设置OPENAI_API_KEY和ADMIN_API_KEY等参数

6. **启动服务**
   ```
   uvicorn app:app --reload
   ```

### Docker部署

1. **构建Docker镜像**
   ```
   docker build -t frmaster-service .
   ```

2. **运行Docker容器**
   ```
   docker run -d --name frmaster-api -p 8000:8000 --env-file .env frmaster-service
   ```

## API接口说明

### 主要接口

- `POST /api/upload` - 上传和处理图片
  - 请求：multipart/form-data，包含图片文件
  - 响应：JSON，包含处理结果和下载链接

- `GET /api/download/{filename}` - 下载处理结果
  - 请求：URL路径中包含文件名
  - 响应：文件内容

### 管理员接口

- `POST /api/admin/update-prompt` - 更新提示词
  - 请求：JSON，包含new_prompt字段
  - 头部：API-Key（管理员密钥）

- `GET /api/admin/get-prompt` - 获取当前提示词
  - 请求：无参数
  - 头部：API-Key（管理员密钥）

## 安全说明

- 所有管理员API调用需要在请求头中包含有效的API-Key
- 提示词使用加密存储，确保安全性
- 上传文件进行类型和大小验证
- 定期清理临时文件
- 输出文件名保留原始文件名，确保一致性

## 使用示例

### 上传图片

```python
import requests

url = "http://localhost:8000/api/upload"
files = {"file": open("carpet_image.jpg", "rb")}

response = requests.post(url, files=files)
print(response.json())
```

### 更新提示词

```python
import requests

url = "http://localhost:8000/api/admin/update-prompt"
headers = {"API-Key": "your_admin_api_key"}
data = {"new_prompt": "新的提示词内容..."}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

## 错误处理

服务会返回适当的HTTP状态码和错误消息：

- 400：请求格式错误
- 403：无效的API密钥
- 404：资源不存在
- 500：服务器内部错误

## 许可证

[MIT许可证](LICENSE) 