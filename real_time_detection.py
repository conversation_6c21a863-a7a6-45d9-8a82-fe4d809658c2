#!/usr/bin/env python3
"""
实时按钮检测工具
用于实时检测Vector Magic界面中的按钮，帮助调试自动化流程
"""

import cv2
import numpy as np
import pyautogui
import time
import threading
import tkinter as tk
from tkinter import ttk
import win32gui
import win32con

class RealTimeDetector:
    def __init__(self):
        self.running = False
        self.detection_thread = None
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("实时按钮检测工具")
        self.root.geometry("600x400")
        
        # 控制面板
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=10)
        
        self.start_btn = tk.Button(control_frame, text="开始检测", 
                                  command=self.start_detection, bg="#4CAF50", fg="white")
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = tk.Button(control_frame, text="停止检测", 
                                 command=self.stop_detection, bg="#f44336", fg="white")
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="截图测试", 
                 command=self.take_screenshot).pack(side=tk.LEFT, padx=5)
        
        # 检测方法选择
        method_frame = tk.Frame(self.root)
        method_frame.pack(pady=10)
        
        tk.Label(method_frame, text="检测方法:").pack(side=tk.LEFT)
        
        self.detection_method = tk.StringVar(value="color")
        methods = [("颜色检测", "color"), ("边缘检测", "edge"), ("模板匹配", "template")]
        
        for text, value in methods:
            tk.Radiobutton(method_frame, text=text, variable=self.detection_method, 
                          value=value).pack(side=tk.LEFT, padx=5)
        
        # 参数调整
        param_frame = tk.Frame(self.root)
        param_frame.pack(pady=10)
        
        tk.Label(param_frame, text="检测阈值:").pack(side=tk.LEFT)
        self.threshold = tk.Scale(param_frame, from_=0.1, to=1.0, resolution=0.1, 
                                 orient=tk.HORIZONTAL, length=200)
        self.threshold.set(0.8)
        self.threshold.pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        result_frame = tk.Frame(self.root)
        result_frame.pack(pady=10, fill=tk.BOTH, expand=True)
        
        tk.Label(result_frame, text="检测结果:", font=("Arial", 12, "bold")).pack(anchor=tk.W)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(text_frame, height=15, width=70)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def log_message(self, message):
        """记录消息到文本框"""
        timestamp = time.strftime("%H:%M:%S")
        self.result_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_detection(self):
        """开始检测"""
        if not self.running:
            self.running = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.status_var.set("检测中...")
            
            self.detection_thread = threading.Thread(target=self.detection_loop)
            self.detection_thread.daemon = True
            self.detection_thread.start()
            
            self.log_message("开始实时检测")
    
    def stop_detection(self):
        """停止检测"""
        self.running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("已停止")
        self.log_message("停止检测")
    
    def detection_loop(self):
        """检测循环"""
        while self.running:
            try:
                # 获取Vector Magic窗口
                vector_window = self.find_vector_magic_window()
                if not vector_window:
                    self.log_message("未找到Vector Magic窗口")
                    time.sleep(2)
                    continue
                
                # 获取窗口区域
                window_rect = win32gui.GetWindowRect(vector_window)
                region = (window_rect[0], window_rect[1], 
                         window_rect[2] - window_rect[0], 
                         window_rect[3] - window_rect[1])
                
                # 截取窗口
                screenshot = pyautogui.screenshot(region=region)
                screenshot_np = np.array(screenshot)
                
                # 根据选择的方法进行检测
                method = self.detection_method.get()
                threshold = self.threshold.get()
                
                if method == "color":
                    buttons = self.detect_buttons_by_color(screenshot_np, region)
                elif method == "edge":
                    buttons = self.detect_buttons_by_edge(screenshot_np, region)
                elif method == "template":
                    buttons = self.detect_buttons_by_template(screenshot_np, region, threshold)
                
                if buttons:
                    self.log_message(f"检测到 {len(buttons)} 个按钮: {buttons}")
                
                time.sleep(1)  # 每秒检测一次
                
            except Exception as e:
                self.log_message(f"检测错误: {e}")
                time.sleep(2)
    
    def find_vector_magic_window(self):
        """查找Vector Magic窗口"""
        vector_window = None
        def enum_windows(hwnd, ctx):
            try:
                window_text = win32gui.GetWindowText(hwnd)
                if "Vector Magic" in window_text or "vmde" in window_text.lower():
                    nonlocal vector_window
                    vector_window = hwnd
                    return False
            except:
                pass
            return True
        
        win32gui.EnumWindows(enum_windows, None)
        return vector_window
    
    def detect_buttons_by_color(self, image, region):
        """通过颜色检测按钮"""
        try:
            # 转换为HSV
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            
            # 定义按钮颜色范围
            button_colors = {
                "蓝色按钮": [(100, 50, 50), (130, 255, 255)],
                "绿色按钮": [(40, 50, 50), (80, 255, 255)],
                "灰色按钮": [(0, 0, 50), (180, 30, 200)]
            }
            
            detected_buttons = []
            
            for color_name, (lower, upper) in button_colors.items():
                lower = np.array(lower)
                upper = np.array(upper)
                
                mask = cv2.inRange(hsv, lower, upper)
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 500:  # 最小按钮面积
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h
                        if 0.5 <= aspect_ratio <= 4.0:  # 按钮长宽比
                            center_x = region[0] + x + w // 2
                            center_y = region[1] + y + h // 2
                            detected_buttons.append(f"{color_name}({center_x},{center_y})")
            
            return detected_buttons
            
        except Exception as e:
            self.log_message(f"颜色检测错误: {e}")
            return []
    
    def detect_buttons_by_edge(self, image, region):
        """通过边缘检测按钮"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            detected_buttons = []
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 500:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.5 <= aspect_ratio <= 4.0:
                        center_x = region[0] + x + w // 2
                        center_y = region[1] + y + h // 2
                        detected_buttons.append(f"边缘按钮({center_x},{center_y})")
            
            return detected_buttons
            
        except Exception as e:
            self.log_message(f"边缘检测错误: {e}")
            return []
    
    def detect_buttons_by_template(self, image, region, threshold):
        """通过模板匹配检测按钮"""
        try:
            import os
            detected_buttons = []
            
            # 检查按钮图片目录
            if not os.path.exists("button_images"):
                return []
            
            # 遍历所有按钮模板
            for filename in os.listdir("button_images"):
                if filename.endswith(".png"):
                    template_path = os.path.join("button_images", filename)
                    template = cv2.imread(template_path)
                    
                    if template is not None:
                        # 模板匹配
                        result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
                        locations = np.where(result >= threshold)
                        
                        for pt in zip(*locations[::-1]):
                            center_x = region[0] + pt[0] + template.shape[1] // 2
                            center_y = region[1] + pt[1] + template.shape[0] // 2
                            button_name = filename.replace(".png", "")
                            detected_buttons.append(f"{button_name}({center_x},{center_y})")
            
            return detected_buttons
            
        except Exception as e:
            self.log_message(f"模板匹配错误: {e}")
            return []
    
    def take_screenshot(self):
        """截图测试"""
        try:
            vector_window = self.find_vector_magic_window()
            if vector_window:
                window_rect = win32gui.GetWindowRect(vector_window)
                region = (window_rect[0], window_rect[1], 
                         window_rect[2] - window_rect[0], 
                         window_rect[3] - window_rect[1])
                
                screenshot = pyautogui.screenshot(region=region)
                filename = f"vector_magic_screenshot_{int(time.time())}.png"
                screenshot.save(filename)
                self.log_message(f"截图已保存: {filename}")
            else:
                self.log_message("未找到Vector Magic窗口")
                
        except Exception as e:
            self.log_message(f"截图失败: {e}")
    
    def run(self):
        """运行检测器"""
        self.root.mainloop()

def main():
    """主函数"""
    print("实时按钮检测工具")
    print("请确保Vector Magic已经启动")
    
    detector = RealTimeDetector()
    detector.run()

if __name__ == "__main__":
    main()
