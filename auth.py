from datetime import datetime
from fastapi import Depends, HTTPException, status, Request, Response
from typing import Optional, Dict
import secrets

import config
from models import User, UserDB, UserRole
from database import db

# 用户数据库实例
user_db = UserDB()

# 定义SESSION_ID、SESSION_COOKIE和SESSION_STORE
SESSION_ID = "frmaster_session"
SESSION_COOKIE_MAX_AGE = 86400  # 1天有效期
# 内存中的session存储 {session_id: username}
SESSION_STORE = {}
# 用户名到session_id的映射 {username: session_id}
USER_SESSIONS: Dict[str, str] = {}

def create_session(response: Response, username: str) -> str:
    """创建会话，设置cookie"""
    # 生成随机session_id
    session_id = secrets.token_hex(16)
    
    # 如果用户已经有活跃会话，先删除旧会话
    if username in USER_SESSIONS:
        old_session_id = USER_SESSIONS[username]
        if old_session_id in SESSION_STORE:
            del SESSION_STORE[old_session_id]
    
    # 存储在SESSION_STORE
    SESSION_STORE[session_id] = username
    # 更新用户会话映射
    USER_SESSIONS[username] = session_id
    
    # 设置cookie
    response.set_cookie(
        key=SESSION_ID,
        value=session_id,
        max_age=SESSION_COOKIE_MAX_AGE,
        httponly=True,
        samesite="lax"
    )
    
    return session_id

def delete_session(response: Response, session_id: str) -> None:
    """删除会话"""
    if session_id in SESSION_STORE:
        username = SESSION_STORE[session_id]
        del SESSION_STORE[session_id]
        
        # 如果用户的当前会话是这个会话，也从USER_SESSIONS中删除
        if username in USER_SESSIONS and USER_SESSIONS[username] == session_id:
            del USER_SESSIONS[username]
    
    # 删除cookie
    response.delete_cookie(key=SESSION_ID)

async def get_current_user(request: Request) -> Optional[User]:
    """从cookie获取当前用户"""
    session_id = request.cookies.get(SESSION_ID)
    
    if not session_id or session_id not in SESSION_STORE:
        return None
    
    username = SESSION_STORE[session_id]
    # 检查这是否是用户的最新会话
    if username not in USER_SESSIONS or USER_SESSIONS[username] != session_id:
        # 会话已被其他登录踢出
        return None
    
    user = user_db.get_user_by_username(username)
    
    if not user:
        return None
    
    if not user.is_active:
        return None
        
    # 检查账户是否过期
    if user.expiry_date and user.expiry_date < datetime.now():
        return None
    
    return user

async def get_current_active_user(request: Request) -> User:
    """获取当前活跃用户，如果没有登录或被禁用则抛出异常"""
    user = await get_current_user(request)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="请先登录"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户已被禁用"
        )
    
    return user

async def get_admin_user(request: Request) -> User:
    """获取管理员用户（用于需要管理员权限的接口）"""
    user = await get_current_user(request)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="请先登录"
        )
    
    if user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    return user

def authenticate_user(username: str, password: str) -> Optional[User]:
    """验证用户凭据"""
    user = user_db.get_user_by_username(username)
    if not user:
        return None
    if not user.verify_password(password):
        return None
    
    # 检查账户是否过期
    if user.expiry_date and user.expiry_date < datetime.now():
        return None
        
    return user

# 初始化：创建默认管理员用户（如果不存在）
user_db.create_admin_if_not_exists() 