<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>地毯图案提取系统 - 用户仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        .dragover {
            border-color: #0d6efd;
            background-color: #e9f2ff;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">地毯图案提取系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/vectorize">矢量图生成</a>
                    </li>
                    <!-- 管理员显示管理入口 -->
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/admin">管理面板</a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/user-management">用户管理</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">欢迎，<span id="username-display">{{ user.username }}</span> <span class="badge bg-light text-dark">积分: <span id="user-points">{{ user.points }}</span></span></span>
                    <a href="/logout" class="btn btn-outline-light" id="logout-btn">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <header class="my-4 text-center">
            <h1>地毯图案提取系统</h1>
            <p class="lead">上传地毯图片，自动提取纯净图案</p>
        </header>
        
        <div class="row">
            <!-- 用户信息卡片 -->
            <div class="col-lg-8 mx-auto mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">账户信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>用户名:</strong> <span id="account-username">{{ user.username }}</span></p>
                                <p><strong>角色:</strong> <span id="account-role">{{ user.role }}</span></p>
                                <p><strong>积分:</strong> <span id="account-points" class="badge bg-primary">{{ user.points }}</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>账户过期时间:</strong> <span id="account-expiry">{{ user.expiry_date }}</span></p>
                                <p><strong>每次使用消耗:</strong> <span class="badge bg-danger">3积分</span></p>
                                <p><strong>上次登录:</strong> <span id="account-last-login">{{ user.last_login }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 密钥兑换卡片 -->
            <div class="col-lg-8 mx-auto mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">兑换密钥</h5>
                    </div>
                    <div class="card-body">
                        <form id="redeem-form" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" id="redeem-key" placeholder="输入兑换密钥" required>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-success w-100">兑换</button>
                            </div>
                        </form>
                        <div class="mt-3">
                            <div class="alert alert-success" id="redeem-success" style="display: none;"></div>
                            <div class="alert alert-danger" id="redeem-error" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">上传图片</h5>
                    </div>
                    <div class="card-body">
                        <div id="images-container">
                            <!-- 第一个图片上传区域 -->
                            <div class="upload-area mb-3" id="upload-area-0">
                                <div class="upload-prompt" id="upload-prompt-0">
                                    <i class="fa fa-cloud-upload fa-3x mb-3"></i>
                                    <h4>拖放图片到此处</h4>
                                    <p>或</p>
                                    <label for="file-input-0" class="btn btn-outline-primary">选择图片</label>
                                    <input id="file-input-0" type="file" accept="image/jpeg,image/png,image/gif" style="display: none" class="file-input">
                                </div>
                                <div class="preview-area" id="preview-area-0" style="display: none;">
                                    <img id="preview-image-0" src="#" alt="预览图">
                                    <div class="preview-controls mt-3">
                                        <button class="btn btn-sm btn-outline-danger remove-btn" data-index="0">移除</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 添加更多图片按钮 -->
                            <div id="add-more-container" class="text-center mb-3">
                                <button id="add-more-btn" class="btn btn-outline-success">
                                    <i class="fa fa-plus"></i> 添加更多图片
                                </button>
                                <div class="small text-muted mt-1">最多添加5张图片</div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-group mb-3">
                                <label for="prompt-select" class="form-label">选择提示词：</label>
                                <select class="form-select" id="prompt-select">
                                    <option value="">加载中...</option>
                                </select>
                                <div class="form-text text-muted">选择合适的提示词可以提高图案提取效果</div>
                            </div>
                            <div class="alert alert-warning mb-2 p-2 small">
                                <i class="fa fa-info-circle"></i> 点击开始处理按钮将<strong>立即扣除每张图片3积分</strong>，无论处理结果如何
                            </div>
                            <button class="btn btn-primary w-100" id="process-btn" disabled>开始处理</button>
                        </div>

                        <div class="file-info mt-3 small text-muted">
                            <p>支持的文件类型: JPG, PNG, JPEG, GIF</p>
                            <p>最大文件大小: 10MB</p>
                        </div>
                    </div>
                </div>

                <!-- 处理状态 -->
                <div class="card shadow-sm mb-4" id="processing-card" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">处理中</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <p id="processing-message">正在提取图案，请稍候...</p>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div class="card shadow-sm mb-4" id="result-card" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">处理结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="results-container">
                            <!-- 结果会动态添加到这里 -->
                        </div>
                        <div class="mt-3 text-center">
                            <button id="new-upload-btn" class="btn btn-outline-secondary">
                                <i class="fa fa-refresh"></i> 上传新图片
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="error-alert" style="display: none;">
                    <strong>错误：</strong> <span id="error-message"></span>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 地毯图案提取系统</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示或隐藏管理员菜单
            if ('{{ user.role }}' === 'admin') {
                document.querySelectorAll('.admin-only').forEach(item => {
                    item.style.display = 'block';
                });
            }
            
            // 所有API请求都自动包含凭据（cookies）
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                options.credentials = 'include'; // 自动包含cookies
                return originalFetch.call(this, url, options);
            };

            // 加载提示词列表
            loadPromptList();
            
            // 格式化显示日期
            formatDates();
            
            // 处理密钥兑换表单
            document.getElementById('redeem-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const redeemKey = document.getElementById('redeem-key').value;
                const username = document.getElementById('account-username').textContent;
                
                try {
                    const response = await fetch('/api/redeem', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            key: redeemKey,
                            username: username
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 显示成功消息
                        const successMsg = document.getElementById('redeem-success');
                        successMsg.textContent = data.message;
                        successMsg.style.display = 'block';
                        
                        // 隐藏错误消息
                        document.getElementById('redeem-error').style.display = 'none';
                        
                        // 更新用户信息
                        document.getElementById('account-points').textContent = data.data.points;
                        
                        if (data.data.expiry_date) {
                            const expiryDate = new Date(data.data.expiry_date);
                            document.getElementById('account-expiry').textContent = expiryDate.toLocaleString();
                        }
                        
                        // 更新导航栏中的积分显示
                        document.getElementById('user-points').textContent = data.data.points;
                        
                        // 清空输入框
                        document.getElementById('redeem-key').value = '';
                        
                        // 5秒后隐藏成功消息
                        setTimeout(() => {
                            successMsg.style.display = 'none';
                        }, 5000);
                    } else {
                        // 显示错误消息
                        const errorMsg = document.getElementById('redeem-error');
                        errorMsg.textContent = data.message;
                        errorMsg.style.display = 'block';
                        
                        // 隐藏成功消息
                        document.getElementById('redeem-success').style.display = 'none';
                        
                        // 5秒后隐藏错误消息
                        setTimeout(() => {
                            errorMsg.style.display = 'none';
                        }, 5000);
                    }
                } catch (error) {
                    console.error('兑换密钥时出错:', error);
                    
                    // 显示错误消息
                    const errorMsg = document.getElementById('redeem-error');
                    errorMsg.textContent = '兑换密钥时发生错误，请稍后再试';
                    errorMsg.style.display = 'block';
                    
                    // 隐藏成功消息
                    document.getElementById('redeem-success').style.display = 'none';
                    
                    // 5秒后隐藏错误消息
                    setTimeout(() => {
                        errorMsg.style.display = 'none';
                    }, 5000);
                }
            });
        });

        // 格式化日期显示
        function formatDates() {
            const expiryElem = document.getElementById('account-expiry');
            const lastLoginElem = document.getElementById('account-last-login');
            
            if (expiryElem && expiryElem.textContent) {
                try {
                    const expiryDate = new Date(expiryElem.textContent);
                    if (!isNaN(expiryDate.getTime())) {
                        expiryElem.textContent = expiryDate.toLocaleString();
                    }
                } catch (e) {
                    console.error('无法格式化过期时间:', e);
                }
            }
            
            if (lastLoginElem && lastLoginElem.textContent) {
                try {
                    const loginDate = new Date(lastLoginElem.textContent);
                    if (!isNaN(loginDate.getTime())) {
                        lastLoginElem.textContent = loginDate.toLocaleString();
                    }
                } catch (e) {
                    console.error('无法格式化登录时间:', e);
                }
            }
        }

        // 加载提示词列表
        async function loadPromptList() {
            try {
                const response = await fetch('/api/prompts');
                const data = await response.json();

                if (data.success && data.data && data.data.prompts) {
                    const promptSelect = document.getElementById('prompt-select');
                    promptSelect.innerHTML = ''; // 清空现有选项
                    
                    // 添加选项
                    data.data.prompts.forEach(prompt => {
                        const option = document.createElement('option');
                        option.value = prompt.id;
                        option.textContent = prompt.name;
                        
                        // 如果是默认提示词，设为默认选中
                        if (prompt.is_default) {
                            option.selected = true;
                        }
                        
                        promptSelect.appendChild(option);
                    });
                } else {
                    // 如果加载失败，显示一个默认选项
                    const promptSelect = document.getElementById('prompt-select');
                    promptSelect.innerHTML = '<option value="">默认提示词</option>';
                }
            } catch (error) {
                console.error('加载提示词列表失败:', error);
                // 设置默认选项
                const promptSelect = document.getElementById('prompt-select');
                promptSelect.innerHTML = '<option value="">默认提示词</option>';
            }
        }

        // 处理图像
        async function processImage(file) {
            // 显示处理中状态
            document.getElementById('processing-card').style.display = 'block';
            document.getElementById('result-card').style.display = 'none';
            document.getElementById('error-alert').style.display = 'none';
            
            try {
                // 首先上传文件
                const uploadFormData = new FormData();
                uploadFormData.append('file', file);
                
                const uploadResponse = await fetch('/api/upload', {
                    method: 'POST',
                    body: uploadFormData
                });
                
                const uploadResult = await uploadResponse.json();
                if (!uploadResult.success) {
                    throw new Error(uploadResult.message);
                }
                
                // 获取选中的提示词ID
                const promptSelect = document.getElementById('prompt-select');
                const promptId = promptSelect.value;
                
                // 使用上传的文件路径和选定提示词处理图像
                const processFormData = new FormData();
                processFormData.append('file_path', uploadResult.data.file_path);
                if (promptId) {
                    processFormData.append('prompt_id', promptId);
                }
                
                const processResponse = await fetch('/api/process', {
                    method: 'POST',
                    body: processFormData
                });
                
                const processResult = await processResponse.json();
                if (!processResult.success) {
                    throw new Error(processResult.message);
                }
                
                // 显示处理结果
                document.getElementById('processing-card').style.display = 'none';
                document.getElementById('result-card').style.display = 'block';
                
                // 设置原图和结果图
                document.getElementById('original-image').src = `/uploads/${uploadResult.data.filename}`;
                document.getElementById('result-image').src = `/outputs/${processResult.data.result_file}`;
                
                // 设置下载链接
                const downloadBtn = document.getElementById('download-btn');
                downloadBtn.href = `/api/download/${processResult.data.result_file}`;
                downloadBtn.download = processResult.data.result_file;
                
            } catch (error) {
                console.error('处理图像时出错:', error);
                document.getElementById('processing-card').style.display = 'none';
                
                // 显示错误信息
                document.getElementById('error-alert').style.display = 'block';
                document.getElementById('error-message').textContent = error.message || '处理图像时出现未知错误';
            }
        }
    </script>
</body>
</html> 