document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    const MAX_IMAGES = 5; // 最大图片数量
    let currentImageCount = 1; // 当前图片数量，默认有1个上传区域
    const uploadedFiles = {}; // 存储上传的文件 {index: File}
    const processResults = {}; // 存储处理结果 {index: {original: path, result: path, apiCallId: id}}
    
    // 元素获取
    const imagesContainer = document.getElementById('images-container');
    const addMoreBtn = document.getElementById('add-more-btn');
    const processBtn = document.getElementById('process-btn');
    const processingCard = document.getElementById('processing-card');
    const resultCard = document.getElementById('result-card');
    const resultsContainer = document.getElementById('results-container');
    const newUploadBtn = document.getElementById('new-upload-btn');
    const errorAlert = document.getElementById('error-alert');
    const errorMessage = document.getElementById('error-message');
    
    // 添加更多图片按钮点击事件
    addMoreBtn.addEventListener('click', function() {
        if (currentImageCount < MAX_IMAGES) {
            addNewImageUploader();
            
            // 如果已达到最大数量，禁用添加按钮
            if (currentImageCount >= MAX_IMAGES) {
                addMoreBtn.disabled = true;
                addMoreBtn.classList.add('disabled');
            }
        }
    });
    
    // 处理按钮点击事件
    processBtn.addEventListener('click', function() {
        if (Object.keys(uploadedFiles).length > 0) {
            // 提醒用户会扣除的积分
            const pointsNeeded = Object.keys(uploadedFiles).length * 3;
            if (confirm(`将处理 ${Object.keys(uploadedFiles).length} 张图片，共消耗 ${pointsNeeded} 积分，确定继续吗？`)) {
                processAllImages();
            }
        }
    });
    
    // 添加新的图片上传区域
    function addNewImageUploader() {
        const index = currentImageCount;
        
        // 创建新的上传区域HTML
        const uploadAreaHtml = `
            <div class="upload-area mb-3" id="upload-area-${index}">
                <div class="upload-prompt" id="upload-prompt-${index}">
                    <i class="fa fa-cloud-upload fa-3x mb-3"></i>
                    <h4>拖放图片到此处</h4>
                    <p>或</p>
                    <label for="file-input-${index}" class="btn btn-outline-primary">选择图片</label>
                    <input id="file-input-${index}" type="file" accept="image/jpeg,image/png,image/gif" style="display: none" class="file-input">
                </div>
                <div class="preview-area" id="preview-area-${index}" style="display: none;">
                    <img id="preview-image-${index}" src="#" alt="预览图">
                    <div class="preview-controls mt-3">
                        <button class="btn btn-sm btn-outline-danger remove-btn" data-index="${index}">移除</button>
                    </div>
                </div>
            </div>
        `;
        
        // 将新区域插入到添加按钮之前
        const addMoreContainer = document.getElementById('add-more-container');
        addMoreContainer.insertAdjacentHTML('beforebegin', uploadAreaHtml);
        
        // 获取新创建的元素
        const uploadArea = document.getElementById(`upload-area-${index}`);
        const fileInput = document.getElementById(`file-input-${index}`);
        const removeBtn = uploadArea.querySelector(`.remove-btn[data-index="${index}"]`);
        
        // 设置拖放事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, function() {
                uploadArea.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, function() {
                uploadArea.classList.remove('dragover');
            }, false);
        });
        
        // 拖放文件处理
        uploadArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length) {
                handleFiles(files, index);
            }
        }, false);
        
        // 文件选择处理
        fileInput.addEventListener('change', function() {
            if (this.files.length) {
                handleFiles(this.files, index);
            }
        });
        
        // 移除按钮事件
        removeBtn.addEventListener('click', function() {
            resetUploadForm(index);
        });
        
        currentImageCount++;
    }
    
    // 初始化第一个上传区域的事件
    function initFirstUploader() {
        const index = 0;
        const uploadArea = document.getElementById(`upload-area-${index}`);
        const fileInput = document.getElementById(`file-input-${index}`);
        const removeBtn = uploadArea.querySelector(`.remove-btn[data-index="${index}"]`);
        
        // 设置拖放事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, function() {
                uploadArea.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, function() {
                uploadArea.classList.remove('dragover');
            }, false);
        });
        
        // 拖放文件处理
        uploadArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length) {
                handleFiles(files, index);
            }
        }, false);
        
        // 文件选择处理
        fileInput.addEventListener('change', function() {
            if (this.files.length) {
                handleFiles(this.files, index);
            }
        });
        
        // 移除按钮事件
        removeBtn.addEventListener('click', function() {
            resetUploadForm(index);
        });
    }
    
    // 上传新图片按钮
    newUploadBtn.addEventListener('click', function() {
        resetAll();
    });
    
    // 处理文件
    function handleFiles(files, index) {
        const file = files[0]; // 只处理第一个文件
        
        // 检查文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            showError('不支持的文件类型，请上传JPG, PNG或GIF图片。');
            return;
        }
        
        // 检查文件大小（10MB限制）
        if (file.size > 10 * 1024 * 1024) {
            showError('文件过大，请上传小于10MB的图片。');
            return;
        }

        // 预览图片
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(`preview-image-${index}`).src = e.target.result;
            document.getElementById(`upload-prompt-${index}`).style.display = 'none';
            document.getElementById(`preview-area-${index}`).style.display = 'block';
        };
        reader.readAsDataURL(file);
        
        // 存储文件
        uploadedFiles[index] = file;
        
        // 更新处理按钮状态
        updateProcessButtonState();
        
        // 隐藏错误信息
        hideError();
    }

    // 更新处理按钮状态
    function updateProcessButtonState() {
        processBtn.disabled = Object.keys(uploadedFiles).length === 0;
    }
    
    // 处理所有图片
    function processAllImages() {
        // 显示处理中状态
        processingCard.style.display = 'block';
        processBtn.disabled = true;
        
        // 隐藏上传区域
        for (let i = 0; i < currentImageCount; i++) {
            const uploadArea = document.getElementById(`upload-area-${i}`);
            if (uploadArea) {
                uploadArea.style.display = 'none';
            }
        }
        
        // 隐藏添加更多按钮
        document.getElementById('add-more-container').style.display = 'none';
        
        // 获取选中的提示词ID
        const promptSelect = document.getElementById('prompt-select');
        const selectedPromptId = promptSelect ? promptSelect.value : '';
        
        // 记录待处理的图片数量
        let totalImages = Object.keys(uploadedFiles).length;
        let processedImages = 0;
        
        // 清空结果容器
        resultsContainer.innerHTML = '';
        
        // 依次处理每张图片
        for (const [index, file] of Object.entries(uploadedFiles)) {
            processImage(parseInt(index), file, selectedPromptId, function(success) {
                processedImages++;
                
                // 所有图片处理完成后
                if (processedImages === totalImages) {
                    processingCard.style.display = 'none';
                    resultCard.style.display = 'block';
                }
            });
        }
    }
    
    // 处理单张图片
    function processImage(index, file, promptId, callback) {
        // 创建表单数据
        const formData = new FormData();
        formData.append('file', file);

        // 发送API请求上传文件
        fetch('/api/upload', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应错误: ' + response.status);
            }
            return response.json();
        })
        .then(uploadResult => {
            if (uploadResult.success) {
                // 处理图像
                const processFormData = new FormData();
                processFormData.append('file_path', uploadResult.data.file_path);
                
                // 如果选择了提示词，加入请求
                if (promptId) {
                    processFormData.append('prompt_id', promptId);
                }
                
                return fetch('/api/process', {
                    method: 'POST',
                    body: processFormData,
                    credentials: 'include'
                }).then(response => {
                    if (!response.ok) {
                        throw new Error('处理图像时服务器响应错误: ' + response.status);
                    }
                    return response.json();
                }).then(processResult => {
                    if (processResult.success) {
                        return {
                            processResult,
                            uploadResult,
                            index
                        };
                    } else {
                        throw new Error(processResult.message || '处理失败');
                    }
                });
            } else {
                throw new Error(uploadResult.message || '上传失败');
            }
        })
        .then(result => {
            const { processResult, uploadResult, index } = result;
            
            // 保存处理结果
            processResults[index] = {
                original: uploadResult.data.filename,
                result: processResult.data.result_file,
                apiCallId: processResult.data.api_call_id
            };
            
            // 添加结果到容器
            addResultToContainer(index);
            
            // 处理完成回调
            if (callback) callback(true);
        })
        .catch(error => {
            console.error('处理图片时出错:', error);
            
            // 添加错误结果到容器
            addErrorResultToContainer(index, error.message);
            
            // 处理完成回调
            if (callback) callback(false);
        });
    }
    
    // 添加结果到容器
    function addResultToContainer(index) {
        const result = processResults[index];
        if (!result) return;
        
        const resultHtml = `
            <div class="result-item mb-4" id="result-item-${index}">
                <h5 class="text-center mb-3">图片 #${parseInt(index) + 1} 处理结果</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>原图</h6>
                        <div class="original-image-container">
                            <img src="/uploads/${result.original}" alt="原图" class="img-fluid rounded">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>提取结果</h6>
                        <div class="result-image-container">
                            <img src="/outputs/${result.result}" alt="提取结果" class="img-fluid rounded">
                            <div class="mt-2 d-flex justify-content-center">
                                <button class="refresh-status-btn btn btn-sm btn-info" data-status="stopped" data-index="${index}">
                                    <i class="fa fa-refresh"></i> 检查处理状态
                                </button>
                                <div class="spinner-border spinner-border-sm ms-2 loading-spinner" role="status" style="display: none;">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <a href="/api/download/${result.result}" class="btn btn-primary download-btn" download>
                        <i class="fa fa-download"></i> 下载结果
                    </a>
                </div>
            </div>
            <hr>
        `;
        
        // 添加到结果容器
        resultsContainer.insertAdjacentHTML('beforeend', resultHtml);
        
        // 添加检查状态按钮的事件
        const refreshBtn = resultsContainer.querySelector(`#result-item-${index} .refresh-status-btn`);
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                const status = this.getAttribute('data-status');
                const idx = this.getAttribute('data-index');
                const resultData = processResults[idx];
                const loadingSpinner = this.nextElementSibling;
                
                if (status === 'stopped') {
                    // 开始检查状态
                    if (resultData && resultData.apiCallId) {
                        this.setAttribute('data-status', 'checking');
                        this.innerHTML = '<i class="fa fa-stop"></i> 停止检查';
                        this.classList.remove('btn-info');
                        this.classList.add('btn-warning');
                        loadingSpinner.style.display = 'inline-block';
                        
                        // 开始检查状态
                        checkProcessStatus(resultData.apiCallId, idx);
                    }
                } else {
                    // 停止检查状态
                    this.setAttribute('data-status', 'stopped');
                    this.innerHTML = '<i class="fa fa-refresh"></i> 检查处理状态';
                    this.classList.remove('btn-warning');
                    this.classList.add('btn-info');
                    loadingSpinner.style.display = 'none';
                }
            });
        }
    }
    
    // 添加错误结果到容器
    function addErrorResultToContainer(index, errorMsg) {
        const resultHtml = `
            <div class="result-item mb-4" id="result-item-${index}">
                <h5 class="text-center mb-3">图片 #${parseInt(index) + 1} 处理失败</h5>
                <div class="alert alert-danger">
                    ${errorMsg || '处理图片时出错'}
                </div>
            </div>
            <hr>
        `;
        
        // 添加到结果容器
        resultsContainer.insertAdjacentHTML('beforeend', resultHtml);
    }
    
    // 检查处理状态
    function checkProcessStatus(apiCallId, index) {
        // 发送请求检查状态
        fetch(`/api/process/status/${apiCallId}`, {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取处理状态失败');
            }
            return response.json();
        })
        .then(statusResult => {
            if (statusResult.success) {
                console.log('处理状态:', statusResult.data.status);
                
                const resultItem = document.getElementById(`result-item-${index}`);
                if (!resultItem) return;
                
                const refreshBtn = resultItem.querySelector('.refresh-status-btn');
                const loadingSpinner = resultItem.querySelector('.loading-spinner');
                const resultImage = resultItem.querySelector('.result-image-container img');
                const downloadBtn = resultItem.querySelector('.download-btn');
                
                if (statusResult.data.status === 'success') {
                    // 处理完成
                    if (refreshBtn) {
                        refreshBtn.innerHTML = '<i class="fa fa-check"></i> 处理完成';
                        refreshBtn.setAttribute('data-status', 'stopped');
                        refreshBtn.classList.remove('btn-warning');
                        refreshBtn.classList.add('btn-success');
                        loadingSpinner.style.display = 'none';
                    }
                    
                    // 更新结果图
                    if (statusResult.data.result_file && resultImage) {
                        resultImage.src = `/outputs/${statusResult.data.result_file}`;
                        // 更新下载链接
                        if (downloadBtn) {
                            downloadBtn.href = `/api/download/${statusResult.data.result_file}`;
                            downloadBtn.download = statusResult.data.result_file;
                        }
                        
                        // 更新存储的结果
                        if (processResults[index]) {
                            processResults[index].result = statusResult.data.result_file;
                        }
                    }
                } else if (statusResult.data.status === 'error') {
                    // 处理失败
                    if (refreshBtn) {
                        refreshBtn.innerHTML = '<i class="fa fa-times"></i> 处理失败';
                        refreshBtn.setAttribute('data-status', 'stopped');
                        refreshBtn.classList.remove('btn-warning');
                        refreshBtn.classList.add('btn-danger');
                        loadingSpinner.style.display = 'none';
                    }
                } else {
                    // 状态仍为pending，继续检查
                    setTimeout(() => {
                        // 如果按钮状态仍为checking，则继续检查
                        if (refreshBtn && refreshBtn.getAttribute('data-status') === 'checking') {
                            checkProcessStatus(apiCallId, index);
                        }
                    }, 5000); // 5秒后再次检查
                }
            }
        })
        .catch(error => {
            console.error('检查状态出错:', error);
        });
    }
    
    // 重置上传表单
    function resetUploadForm(index) {
        const uploadPrompt = document.getElementById(`upload-prompt-${index}`);
        const previewArea = document.getElementById(`preview-area-${index}`);
        const previewImage = document.getElementById(`preview-image-${index}`);
        const fileInput = document.getElementById(`file-input-${index}`);
        
        if (uploadPrompt && previewArea && previewImage && fileInput) {
            previewArea.style.display = 'none';
            uploadPrompt.style.display = 'block';
            previewImage.src = '#';
            fileInput.value = '';
            
            // 从存储中移除
            if (uploadedFiles[index]) {
                delete uploadedFiles[index];
            }
            
            // 更新处理按钮状态
            updateProcessButtonState();
        }
    }
    
    // 重置所有状态
    function resetAll() {
        // 显示所有上传区域
        for (let i = 0; i < currentImageCount; i++) {
            const uploadArea = document.getElementById(`upload-area-${i}`);
            if (uploadArea) {
                uploadArea.style.display = 'block';
                resetUploadForm(i);
            }
        }
        
        // 显示添加更多按钮
        document.getElementById('add-more-container').style.display = 'block';
        addMoreBtn.disabled = currentImageCount >= MAX_IMAGES;
        
        // 移除额外添加的上传区域
        if (currentImageCount > 1) {
            for (let i = 1; i < currentImageCount; i++) {
                const uploadArea = document.getElementById(`upload-area-${i}`);
                if (uploadArea) {
                    uploadArea.remove();
                }
            }
            currentImageCount = 1;
            addMoreBtn.disabled = false;
            addMoreBtn.classList.remove('disabled');
        }
        
        // 清空存储
        Object.keys(uploadedFiles).forEach(key => {
            delete uploadedFiles[key];
        });
        Object.keys(processResults).forEach(key => {
            delete processResults[key];
        });
        
        // 隐藏结果卡片
        resultCard.style.display = 'none';
        processingCard.style.display = 'none';
        
        // 清空结果容器
        resultsContainer.innerHTML = '';
        
        // 禁用处理按钮
        processBtn.disabled = true;
        
        // 隐藏错误信息
        hideError();
    }
    
    // 显示错误信息
    function showError(message) {
        errorMessage.innerHTML = message;
        errorAlert.style.display = 'block';
    }
    
    // 隐藏错误信息
    function hideError() {
        errorAlert.style.display = 'none';
    }
    
    // 防止默认事件
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 初始化
    function init() {
        initFirstUploader();
        
        // 显示或隐藏管理员菜单
        if (document.querySelector('.admin-only') && document.getElementById('account-role')) {
            const userRole = document.getElementById('account-role').textContent.trim();
            if (userRole === 'admin') {
                document.querySelectorAll('.admin-only').forEach(item => {
                    item.style.display = 'block';
                });
            }
        }
        
        // 加载提示词列表
        loadPromptList();
        
        // 格式化显示日期
        formatDates();
    }
    
    // 加载提示词列表
    async function loadPromptList() {
        try {
            const response = await fetch('/api/prompts');
            const data = await response.json();

            if (data.success && data.data && data.data.prompts) {
                const promptSelect = document.getElementById('prompt-select');
                promptSelect.innerHTML = ''; // 清空现有选项
                
                // 添加选项
                data.data.prompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = prompt.name;
                    
                    // 如果是默认提示词，设为默认选中
                    if (prompt.is_default) {
                        option.selected = true;
                    }
                    
                    promptSelect.appendChild(option);
                });
            } else {
                // 如果加载失败，显示一个默认选项
                const promptSelect = document.getElementById('prompt-select');
                promptSelect.innerHTML = '<option value="">默认提示词</option>';
            }
        } catch (error) {
            console.error('加载提示词列表失败:', error);
            // 设置默认选项
            const promptSelect = document.getElementById('prompt-select');
            promptSelect.innerHTML = '<option value="">默认提示词</option>';
        }
    }
    
    // 格式化日期显示
    function formatDates() {
        const expiryElem = document.getElementById('account-expiry');
        const lastLoginElem = document.getElementById('account-last-login');
        
        if (expiryElem && expiryElem.textContent) {
            try {
                const expiryDate = new Date(expiryElem.textContent);
                if (!isNaN(expiryDate.getTime())) {
                    expiryElem.textContent = expiryDate.toLocaleString();
                }
            } catch (e) {
                console.error('无法格式化过期时间:', e);
            }
        }
        
        if (lastLoginElem && lastLoginElem.textContent) {
            try {
                const loginDate = new Date(lastLoginElem.textContent);
                if (!isNaN(loginDate.getTime())) {
                    lastLoginElem.textContent = loginDate.toLocaleString();
                }
            } catch (e) {
                console.error('无法格式化登录时间:', e);
            }
        }
    }
    
    // 初始化应用
    init();
}); 