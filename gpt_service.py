import os
import logging
import base64
import time
import requests
from io import BytesIO
from PIL import Image
import config
from prompt_manager import prompt_manager
import re
from database import db
from file_handler import file_handler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPTService:
    """GPT服务类，使用最新的OpenAI Responses API和image_generation工具"""
    
    def __init__(self):
        self.api_key = self._get_api_key()
        self.api_base = self._get_api_base()
        # 固定使用gpt-image-1模型进行图像处理
        self.model = "gpt-image-1"
        self.max_retries = 1  # 修改为只尝试1次
        self.timeout = getattr(config, 'TIMEOUT', 120)
    
    def _get_api_key(self):
        """从数据库获取API密钥，如果没有则使用配置文件中的默认值"""
        try:
            # 查询数据库中的API密钥设置
            query = "SELECT setting_value FROM system_settings WHERE setting_key = 'openai_api_key' LIMIT 1"
            result = db.fetch_one(query)
            
            if result and result['setting_value']:
                logger.info("从数据库获取API密钥成功")
                return result['setting_value']
            else:
                logger.info("数据库中未找到API密钥，使用配置文件中的默认值")
                return config.OPENAI_API_KEY
        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}，使用配置文件中的默认值")
            return config.OPENAI_API_KEY
    
    def _get_api_base(self):
        """从数据库获取API基础URL，如果没有则使用默认值"""
        try:
            # 查询数据库中的API基础URL设置
            query = "SELECT setting_value FROM system_settings WHERE setting_key = 'openai_api_base' LIMIT 1"
            result = db.fetch_one(query)
            
            if result and result['setting_value']:
                logger.info("从数据库获取API基础URL成功")
                return result['setting_value']
            else:
                logger.info("数据库中未找到API基础URL，使用默认值")
                return "https://api.openai.com/v1"
        except Exception as e:
            logger.error(f"获取API基础URL时出错: {str(e)}，使用默认值")
            return "https://api.openai.com/v1"
    
    def process_image(self, image_path, prompt_text):
        """使用最新的OpenAI Image API处理地毯图片，提取平面图案"""
        try:
            # 获取最新的API密钥和基础URL
            self.api_key = self._get_api_key()
            self.api_base = self._get_api_base()
            
            # 检查API密钥
            if not self.api_key:
                return None, "OpenAI API密钥未设置，请在管理界面配置API密钥"
            
            # 获取原始文件名
            original_filename = os.path.basename(image_path)
            
            # 尝试图像编辑接口（如果支持）
            if self.model == "gpt-image-1" or self.model == "dall-e-3":
                logger.info(f"使用{self.model}模型的图像编辑接口")
                image_data, error = self._process_with_image_edit(image_path, prompt_text)
                if image_data and not error:
                    # 保存处理结果
                    output_path, output_filename = file_handler.save_output_file(image_data, original_filename)
                    if output_path:
                        return output_path, None
                    else:
                        return None, "保存处理结果失败"
                else:
                    return None, error or "图像处理失败"
            
            # 备用方法：使用base64编码图片的生成接口
            # 读取并编码图片
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # 使用图像生成API
            image_data, error = self._generate_with_responses_api(base64_image, prompt_text)
            if image_data and not error:
                # 保存处理结果
                output_path, output_filename = file_handler.save_output_file(image_data, original_filename)
                if output_path:
                    return output_path, None
                else:
                    return None, "保存处理结果失败"
            else:
                return None, error or "图像处理失败"
        
        except Exception as e:
            logger.error(f"图片处理过程中发生错误: {str(e)}")
            return None, f"图片处理过程中发生错误: {str(e)}"
    
    def _process_with_image_edit(self, image_path, prompt_text):
        """使用images/edits接口处理图像"""
        logger.info("使用OpenAI图像编辑API")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 使用传入的提示词
        prompt = prompt_text
        
        # 准备文件数据
        files = {
            'image': ('image.png', open(image_path, 'rb'), 'image/png'),
            'prompt': (None, prompt),
            'n': (None, '1'),
            'size': (None, '1536x1024'),  # 使用1536x1024尺寸
        }
        
        # 添加模型参数
        if self.model == "gpt-image-1":
            files['model'] = (None, 'gpt-image-1')
            files['quality'] = (None, 'high')
        else:
            files['model'] = (None, 'dall-e-3')
            files['quality'] = (None, 'hd')
            files['style'] = (None, 'natural')
        
        try:
            logger.info("调用OpenAI图像编辑API")
            
            # 使用自定义API基础URL
            api_endpoint = f"{self.api_base}/images/edits"
            logger.info(f"使用API端点: {api_endpoint}")
            
            response = requests.post(
                api_endpoint,
                headers=headers,
                files=files,
                timeout=self.timeout
            )
            
            logger.info(f"图像编辑API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("图像编辑API调用成功")
                
                # 处理响应数据 - 默认为URL格式
                if "data" in data and len(data["data"]) > 0:
                    if "b64_json" in data["data"][0]:
                        image_base64 = data["data"][0]["b64_json"]
                        image_data = base64.b64decode(image_base64)
                        logger.info(f"成功获取编辑后的图像，大小: {len(image_data)} 字节")
                        return image_data, None
                    elif "url" in data["data"][0]:
                        # 处理URL格式的响应
                        image_url = data["data"][0]["url"]
                        image_response = requests.get(image_url, timeout=self.timeout)
                        if image_response.status_code == 200:
                            logger.info("编辑后的图像下载成功")
                            return image_response.content, None
                        else:
                            return None, f"编辑后的图像下载失败: {image_response.status_code}"
                else:
                    logger.error("响应中缺少data字段或data为空")
                    return None, "响应格式错误：缺少图像数据"
            else:
                error_text = response.text
                logger.error(f"图像编辑API调用失败: {response.status_code} - {error_text}")
                return None, f"图像编辑API调用失败: {response.status_code} - {error_text}"
        
        except requests.exceptions.Timeout:
            logger.error("图像编辑API请求超时")
            return None, "图像编辑API请求超时"
        except Exception as e:
            logger.error(f"图像编辑API请求异常: {str(e)}")
            return None, f"图像编辑API请求异常: {str(e)}"
    
    def _generate_with_responses_api(self, base64_image, prompt_text):
        """使用OpenAI图像API进行图像生成或编辑"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 使用图像生成API
        logger.info("使用OpenAI图像生成API")
        
        # 使用传入的提示词
        prompt = prompt_text
        
        # 使用v1/images/generations接口
        payload = {
            "model": self.model,
            "prompt": prompt,
            "n": 1,
            "size": "1536x1024"  # 使用1536x1024尺寸
        }
        
        # 添加质量参数 - 根据模型不同使用不同参数
        if self.model == "gpt-image-1":
            payload["quality"] = "high"
        elif self.model == "dall-e-3":
            payload["quality"] = "hd"
            payload["style"] = "natural"
        
        return self._make_api_request_for_image(payload)
    
    def _make_api_request_for_image(self, payload):
        """发起图像API请求"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 检查并移除不必要的参数
        if "response_format" in payload:
            logger.info("移除response_format参数，使用默认的URL响应格式")
            del payload["response_format"]
        
        # 获取API端点
        api_endpoint = f"{self.api_base}/images/generations"  # 使用自定义API基础URL
        logger.info(f"使用API端点: {api_endpoint}")
        
        try:
            logger.info("调用OpenAI图像API")
            
            response = requests.post(
                api_endpoint,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            logger.info(f"API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("API调用成功，解析响应数据")
                
                # 处理响应数据 - 默认为URL格式
                if "data" in data and len(data["data"]) > 0:
                    if "b64_json" in data["data"][0]:
                        image_base64 = data["data"][0]["b64_json"]
                        logger.info("成功获取base64图像数据")
                        try:
                            # 解码base64数据
                            image_data = base64.b64decode(image_base64)
                            logger.info(f"成功解码图像数据，大小: {len(image_data)} 字节")
                            return image_data, None
                        except Exception as decode_error:
                            logger.error(f"base64解码失败: {str(decode_error)}")
                            return None, f"图像解码失败: {str(decode_error)}"
                    elif "url" in data["data"][0]:
                        # 处理URL格式的响应
                        image_url = data["data"][0]["url"]
                        logger.info(f"获取到图像URL: {image_url}")
                        try:
                            # 下载图像
                            image_response = requests.get(image_url, timeout=self.timeout)
                            if image_response.status_code == 200:
                                logger.info("图像下载成功")
                                return image_response.content, None
                            else:
                                return None, f"图像下载失败: {image_response.status_code}"
                        except Exception as download_error:
                            logger.error(f"图像下载失败: {str(download_error)}")
                            return None, f"图像下载失败: {str(download_error)}"
                else:
                    logger.error("响应中缺少data字段或data为空")
                    return None, "响应格式错误：缺少图像数据"
            else:
                error_text = response.text
                logger.error(f"API调用失败: {response.status_code} - {error_text}")
                return None, f"API调用失败: {response.status_code} - {error_text}"
        
        except requests.exceptions.Timeout:
            logger.error("API请求超时")
            return None, "API请求超时"
        except Exception as e:
            logger.error(f"API请求异常: {str(e)}")
            return None, f"API请求异常: {str(e)}"

# 使用OpenAI官方Python客户端的版本（推荐）
class OfficialClientGPTService:
    """使用OpenAI官方Python客户端的服务类，仅使用gpt-image-1模型"""
    
    def __init__(self):
        self.api_key = self._get_api_key()
        self.api_base = self._get_api_base()
        self.model = "gpt-image-1"  # 强制使用gpt-image-1模型
        self.max_retries = getattr(config, 'MAX_RETRIES', 3)
        self.timeout = getattr(config, 'TIMEOUT', 120)
        
        # 导入官方客户端
        try:
            from openai import OpenAI
            self.client = None  # 初始化为None，在需要时创建
            logger.info("使用OpenAI官方Python客户端和gpt-image-1模型")
        except ImportError:
            logger.error("未找到OpenAI官方客户端，请安装: pip install openai")
            raise ImportError("未找到OpenAI官方客户端，请安装: pip install openai")
    
    def _get_api_key(self):
        """从数据库获取API密钥，如果没有则使用配置文件中的默认值"""
        try:
            # 查询数据库中的API密钥设置
            query = "SELECT setting_value FROM system_settings WHERE setting_key = 'openai_api_key' LIMIT 1"
            result = db.fetch_one(query)
            
            if result and result['setting_value']:
                logger.info("从数据库获取API密钥成功")
                return result['setting_value']
            else:
                logger.info("数据库中未找到API密钥，使用配置文件中的默认值")
                return config.OPENAI_API_KEY
        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}，使用配置文件中的默认值")
            return config.OPENAI_API_KEY
    
    def _get_api_base(self):
        """从数据库获取API基础URL，如果没有则使用默认值"""
        try:
            # 查询数据库中的API基础URL设置
            query = "SELECT setting_value FROM system_settings WHERE setting_key = 'openai_api_base' LIMIT 1"
            result = db.fetch_one(query)
            
            if result and result['setting_value']:
                logger.info("从数据库获取API基础URL成功")
                return result['setting_value']
            else:
                logger.info("数据库中未找到API基础URL，使用默认值")
                return None  # 官方客户端默认使用 https://api.openai.com/v1
        except Exception as e:
            logger.error(f"获取API基础URL时出错: {str(e)}，使用默认值")
            return None
    
    def _initialize_client(self):
        """初始化OpenAI客户端"""
        from openai import OpenAI
        self.api_key = self._get_api_key()  # 获取最新的API密钥
        self.api_base = self._get_api_base()  # 获取最新的API基础URL
        
        # 创建客户端实例，如果有自定义API基础URL则使用
        if self.api_base:
            self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)
            logger.info(f"使用自定义API基础URL: {self.api_base}")
        else:
            self.client = OpenAI(api_key=self.api_key)
            logger.info("使用默认API基础URL")
    
    def process_image(self, image_path, prompt_text):
        """使用官方客户端处理图像，仅使用gpt-image-1模型"""
        try:
            # 初始化客户端（确保使用最新的API密钥和基础URL）
            self._initialize_client()
            
            if not self.api_key:
                return None, "OpenAI API密钥未设置，请在管理界面配置API密钥"
            
            # 获取原始文件名
            original_filename = os.path.basename(image_path)
            
            # 首先尝试图像编辑API
            try:
                image_data, error = self._process_with_image_edit_client(image_path, prompt_text)
                if image_data and not error:
                    # 保存处理结果
                    output_path, output_filename = file_handler.save_output_file(image_data, original_filename)
                    if output_path:
                        return output_path, None
                    else:
                        return None, "保存处理结果失败"
                else:
                    logger.warning(f"使用官方客户端的图像编辑API失败: {error}，尝试图像生成API")
                    # 如果编辑API失败，尝试图像生成API
                    image_data, error = self._process_with_image_generation_client(prompt_text)
                    if image_data and not error:
                        # 保存处理结果
                        output_path, output_filename = file_handler.save_output_file(image_data, original_filename)
                        if output_path:
                            return output_path, None
                        else:
                            return None, "保存处理结果失败"
                    else:
                        return None, error or "图像处理失败"
            except Exception as edit_error:
                logger.warning(f"使用官方客户端的图像编辑API失败: {str(edit_error)}，尝试图像生成API")
                # 如果编辑API失败，尝试图像生成API
                image_data, error = self._process_with_image_generation_client(prompt_text)
                if image_data and not error:
                    # 保存处理结果
                    output_path, output_filename = file_handler.save_output_file(image_data, original_filename)
                    if output_path:
                        return output_path, None
                    else:
                        return None, "保存处理结果失败"
                else:
                    return None, error or "图像处理失败"
                
        except Exception as e:
            logger.error(f"官方客户端处理失败: {str(e)}")
            return None, f"官方客户端处理失败: {str(e)}"
    
    def _process_with_image_edit_client(self, image_path, prompt_text):
        """使用官方客户端的图像编辑API，仅使用gpt-image-1模型"""
        logger.info("使用官方客户端的图像编辑API，模型: gpt-image-1")
        
        # 使用传入的提示词
        prompt = prompt_text
        
        try:
            # 打开图像文件
            with open(image_path, "rb") as image_file:
                # 使用官方客户端调用图像编辑API，仅使用gpt-image-1
                response = self.client.images.edit(
                    model="gpt-image-1",
                    image=image_file,
                    prompt=prompt,
                    n=1,
                    size="1536x1024",  # 使用1536x1024尺寸
                    quality="high"
                )
                
                # 处理响应
                if hasattr(response, "data") and len(response.data) > 0:
                    if hasattr(response.data[0], "b64_json") and response.data[0].b64_json:
                        # 解码Base64数据
                        image_data = base64.b64decode(response.data[0].b64_json)
                        logger.info(f"官方客户端图像编辑成功，图像大小: {len(image_data)} 字节")
                        return image_data, None
                    elif hasattr(response.data[0], "url") and response.data[0].url:
                        # 下载URL图像
                        image_url = response.data[0].url
                        image_response = requests.get(image_url, timeout=self.timeout)
                        if image_response.status_code == 200:
                            logger.info("官方客户端编辑图像URL下载成功")
                            return image_response.content, None
                        else:
                            return None, f"图像URL下载失败: {image_response.status_code}"
                
                return None, "官方客户端图像编辑响应中没有找到图像数据"
                
        except Exception as e:
            logger.error(f"官方客户端图像编辑失败: {str(e)}")
            raise e
    
    def _process_with_image_generation_client(self, prompt_text):
        """使用官方客户端的图像生成API，仅使用gpt-image-1模型"""
        logger.info("使用官方客户端的图像生成API，模型: gpt-image-1")
        
        # 使用传入的提示词
        prompt = prompt_text
        
        try:
            # 使用官方客户端调用图像生成API，仅使用gpt-image-1
            response = self.client.images.generate(
                model="gpt-image-1",
                prompt=prompt,
                n=1,
                size="1536x1024",  # 使用1536x1024尺寸
                quality="high",
                style="natural"
            )
            
            # 处理响应
            if hasattr(response, "data") and len(response.data) > 0:
                if hasattr(response.data[0], "b64_json") and response.data[0].b64_json:
                    # 解码Base64数据
                    image_data = base64.b64decode(response.data[0].b64_json)
                    logger.info(f"官方客户端图像生成成功，图像大小: {len(image_data)} 字节")
                    return image_data, None
                elif hasattr(response.data[0], "url") and response.data[0].url:
                    # 下载URL图像
                    image_url = response.data[0].url
                    image_response = requests.get(image_url, timeout=self.timeout)
                    if image_response.status_code == 200:
                        logger.info("官方客户端生成图像URL下载成功")
                        return image_response.content, None
                    else:
                        return None, f"图像URL下载失败: {image_response.status_code}"
            
            return None, "官方客户端图像生成响应中没有找到图像数据"
            
        except Exception as e:
            logger.error(f"官方客户端图像生成失败: {str(e)}")
            return None, f"官方客户端图像生成失败: {str(e)}"

# 初始化服务实例
gpt_service = GPTService()
official_service = OfficialClientGPTService()

# 推荐使用方式：
# 1. 优先尝试官方客户端: result = official_service.process_image(image_path, filename)
# 2. 备用requests版本: result = gpt_service.process_image(image_path, filename)