# 用户数据迁移到SQLite

本项目已将用户数据存储从JSON文件更改为SQLite数据库，这带来以下好处：

- 更好的数据完整性和安全性
- 支持并发访问
- 更高效的查询和管理
- 更好的扩展性

## 自动迁移

系统会在启动时自动检测是否需要迁移数据：

1. 如果存在 `users.json` 文件，系统会自动将数据迁移到SQLite数据库
2. 迁移成功后，原JSON文件会被重命名为 `users.json.bak` 作为备份
3. 所有用户数据将保存在 `frmaster.db` 数据库文件中

## 手动迁移

如果需要手动执行迁移，可以按照以下步骤操作：

```bash
# 确保环境变量设置正确（如果需要自定义数据库路径）
# export DB_PATH=/path/to/your/database.db

# 运行迁移脚本
python migrate_to_sqlite.py
```

## 数据库位置

默认情况下，SQLite数据库文件保存在项目根目录的 `frmaster.db`。

在Docker环境中，数据库默认保存在 `/app/data/frmaster.db`。

## 验证迁移

要验证迁移是否成功，可以：

1. 检查是否生成了 `frmaster.db` 文件
2. 登录系统确认用户数据是否正确
3. 在管理员面板中查看用户列表

## 注意事项

1. 数据迁移过程中请勿中断服务
2. 迁移完成后，建议保留 `users.json.bak` 备份文件
3. 在生产环境中，建议定期备份数据库文件
4. 如需将数据库文件移动到其他位置，请设置 `DB_PATH` 环境变量

## 数据库结构

用户表结构如下：

```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    hashed_password TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at TEXT NOT NULL,
    last_login TEXT,
    is_active INTEGER NOT NULL
);
``` 