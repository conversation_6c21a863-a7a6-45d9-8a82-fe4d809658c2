import os
import base64
import logging
import json
import sqlite3
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import config
from database import db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PromptManager:
    """提示词管理类，负责提示词的存储、读取和更新"""
    
    def __init__(self):
        # 使用ADMIN_API_KEY作为密钥派生的基础
        self.key = self._get_encryption_key()
        
        # 确保提示词表存在
        self._ensure_prompts_table()
        
        # 如果表中没有提示词，添加默认提示词
        self._add_default_prompt_if_empty()
    
    def _get_encryption_key(self):
        """从ADMIN_API_KEY派生加密密钥"""
        admin_key = config.ADMIN_API_KEY
        if not admin_key:
            # 如果未设置ADMIN_API_KEY，使用默认值（不推荐生产环境）
            admin_key = "default_admin_key_do_not_use_in_production"
            logger.warning("未设置ADMIN_API_KEY，使用默认值，不推荐在生产环境中使用！")
        
        # 使用PBKDF2进行密钥派生
        salt = b'frmaster_service_salt'  # 在生产环境中应使用随机salt并安全存储
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(admin_key.encode()))
        return key
    
    def _ensure_prompts_table(self):
        """确保prompts表存在"""
        try:
            db.execute_query("""
            CREATE TABLE IF NOT EXISTS prompts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                content TEXT NOT NULL,
                encrypted_content BLOB NOT NULL,
                is_default INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            logger.info("确保prompts表存在")
        except Exception as e:
            logger.error(f"创建prompts表失败: {e}")
            raise
    
    def _add_default_prompt_if_empty(self):
        """如果表为空，添加默认提示词"""
        try:
            # 检查是否有提示词
            count = db.fetch_one("SELECT COUNT(*) FROM prompts")[0]
            if count == 0:
                # 添加默认提示词
                self.create_prompt("默认提示词", config.DEFAULT_PROMPT, is_default=1)
                logger.info("已添加默认提示词")
        except Exception as e:
            logger.error(f"添加默认提示词失败: {e}")
    
    def encrypt_prompt(self, prompt_text):
        """加密提示词"""
        try:
            f = Fernet(self.key)
            encrypted_prompt = f.encrypt(prompt_text.encode())
            return encrypted_prompt
        except Exception as e:
            logger.error(f"提示词加密失败: {e}")
            raise
    
    def decrypt_prompt(self, encrypted_prompt):
        """解密提示词"""
        try:
            f = Fernet(self.key)
            decrypted_prompt = f.decrypt(encrypted_prompt).decode()
            return decrypted_prompt
        except Exception as e:
            logger.error(f"提示词解密失败: {e}")
            raise
    
    def get_prompt(self, prompt_id=None, prompt_name=None):
        """
        获取提示词内容
        - 如果指定prompt_id，返回对应ID的提示词
        - 如果指定prompt_name，返回对应名称的提示词
        - 如果都不指定，返回默认提示词
        """
        try:
            query = None
            params = None
            
            if prompt_id:
                query = "SELECT content, encrypted_content FROM prompts WHERE id = ?"
                params = (prompt_id,)
            elif prompt_name:
                query = "SELECT content, encrypted_content FROM prompts WHERE name = ?"
                params = (prompt_name,)
            else:
                query = "SELECT content, encrypted_content FROM prompts WHERE is_default = 1 ORDER BY id DESC LIMIT 1"
            
            result = db.fetch_one(query, params)
            if result:
                return result[0]  # 返回明文内容
            else:
                logger.warning("未找到提示词，返回默认值")
                return config.DEFAULT_PROMPT
        except Exception as e:
            logger.error(f"获取提示词失败: {e}")
            return config.DEFAULT_PROMPT
    
    def list_prompts(self):
        """获取所有提示词列表"""
        try:
            rows = db.fetch_all("""
                SELECT id, name, content, is_default, created_at, updated_at 
                FROM prompts 
                ORDER BY is_default DESC, name ASC
            """)
            return rows
        except Exception as e:
            logger.error(f"获取提示词列表失败: {e}")
            return []
    
    def create_prompt(self, name, content, is_default=0):
        """
        创建新提示词
        - name: 提示词名称，必须唯一
        - content: 提示词内容
        - is_default: 是否设为默认提示词
        """
        try:
            # 检查名称是否已存在
            existing = db.fetch_one("SELECT id FROM prompts WHERE name = ?", (name,))
            if existing:
                return False, f"提示词名称 '{name}' 已存在"
            
            # 加密内容
            encrypted_content = self.encrypt_prompt(content)
            
            # 如果设置为默认，先清除其他默认提示词
            if is_default:
                db.execute_query("UPDATE prompts SET is_default = 0")
            
            # 插入新提示词
            db.execute_query(
                """
                INSERT INTO prompts (name, content, encrypted_content, is_default) 
                VALUES (?, ?, ?, ?)
                """,
                (name, content, encrypted_content, is_default)
            )
            
            return True, "提示词创建成功"
        except Exception as e:
            logger.error(f"创建提示词失败: {e}")
            return False, f"创建提示词失败: {str(e)}"
    
    def update_prompt(self, content, prompt_id=None, prompt_name=None, new_name=None, set_default=False):
        """
        更新提示词
        - content: 新的提示词内容
        - prompt_id: 要更新的提示词ID
        - prompt_name: 要更新的提示词名称
        - new_name: 新的提示词名称(可选)
        - set_default: 是否设置为默认提示词
        """
        try:
            if not prompt_id and not prompt_name:
                return False, "未指定要更新的提示词"
            
            # 加密内容
            encrypted_content = self.encrypt_prompt(content)
            
            # 判断更新条件
            condition = None
            params = None
            
            if prompt_id:
                condition = "id = ?"
                params = (prompt_id,)
            else:
                condition = "name = ?"
                params = (prompt_name,)
            
            # 检查提示词是否存在
            prompt = db.fetch_one(f"SELECT id FROM prompts WHERE {condition}", params)
            if not prompt:
                return False, "提示词不存在"
            
            # 如果提供了新名称，检查名称是否已存在(排除当前提示词)
            if new_name:
                existing = db.fetch_one(
                    "SELECT id FROM prompts WHERE name = ? AND id != ?", 
                    (new_name, prompt[0])
                )
                if existing:
                    return False, f"提示词名称 '{new_name}' 已存在"
            
            # 如果设置为默认，先清除其他默认提示词
            if set_default:
                db.execute_query("UPDATE prompts SET is_default = 0")
            
            # 准备更新语句和参数
            update_clauses = ["content = ?", "encrypted_content = ?", "updated_at = CURRENT_TIMESTAMP"]
            update_params = [content, encrypted_content]
            
            if new_name:
                update_clauses.append("name = ?")
                update_params.append(new_name)
            
            if set_default:
                update_clauses.append("is_default = 1")
            
            update_params.extend(params)  # 添加条件参数
            
            # 执行更新
            db.execute_query(
                f"UPDATE prompts SET {', '.join(update_clauses)} WHERE {condition}",
                tuple(update_params)
            )
            
            return True, "提示词更新成功"
        except Exception as e:
            logger.error(f"更新提示词失败: {e}")
            return False, f"更新提示词失败: {str(e)}"
    
    def delete_prompt(self, prompt_id=None, prompt_name=None):
        """
        删除提示词
        - prompt_id: 要删除的提示词ID
        - prompt_name: 要删除的提示词名称
        """
        try:
            if not prompt_id and not prompt_name:
                return False, "未指定要删除的提示词"
            
            # 判断删除条件
            condition = None
            params = None
            
            if prompt_id:
                condition = "id = ?"
                params = (prompt_id,)
            else:
                condition = "name = ?"
                params = (prompt_name,)
            
            # 检查是否为默认提示词
            prompt = db.fetch_one(f"SELECT is_default FROM prompts WHERE {condition}", params)
            if not prompt:
                return False, "提示词不存在"
            
            if prompt[0] == 1:
                return False, "不能删除默认提示词，请先设置其他提示词为默认"
            
            # 执行删除
            db.execute_query(f"DELETE FROM prompts WHERE {condition}", params)
            
            return True, "提示词删除成功"
        except Exception as e:
            logger.error(f"删除提示词失败: {e}")
            return False, f"删除提示词失败: {str(e)}"
    
    def set_default_prompt(self, prompt_id=None, prompt_name=None):
        """
        设置默认提示词
        - prompt_id: 要设为默认的提示词ID
        - prompt_name: 要设为默认的提示词名称
        """
        try:
            if not prompt_id and not prompt_name:
                return False, "未指定要设为默认的提示词"
            
            # 判断条件
            condition = None
            params = None
            
            if prompt_id:
                condition = "id = ?"
                params = (prompt_id,)
            else:
                condition = "name = ?"
                params = (prompt_name,)
            
            # 检查提示词是否存在
            prompt = db.fetch_one(f"SELECT id FROM prompts WHERE {condition}", params)
            if not prompt:
                return False, "提示词不存在"
            
            # 清除其他默认提示词
            db.execute_query("UPDATE prompts SET is_default = 0")
            
            # 设置新的默认提示词
            db.execute_query(f"UPDATE prompts SET is_default = 1 WHERE {condition}", params)
            
            return True, "默认提示词已设置"
        except Exception as e:
            logger.error(f"设置默认提示词失败: {e}")
            return False, f"设置默认提示词失败: {str(e)}"

# 初始化提示词管理器实例
prompt_manager = PromptManager() 