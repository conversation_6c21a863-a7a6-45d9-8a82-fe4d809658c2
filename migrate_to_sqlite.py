import os
import json
import logging
from datetime import datetime
from database import db
from models import User, UserRole

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_users_from_json_to_sqlite():
    """将JSON文件中的用户数据迁移到SQLite数据库"""
    json_file = "users.json"
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file):
        logger.warning(f"JSON文件 {json_file} 不存在，无需迁移")
        return
    
    try:
        # 读取JSON文件
        with open(json_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        if "users" not in data or not isinstance(data["users"], list):
            logger.error("JSON文件格式不正确")
            return
        
        # 统计信息
        total_users = len(data["users"])
        migrated_users = 0
        
        logger.info(f"开始迁移用户数据，共 {total_users} 个用户")
        
        # 迁移每个用户
        for user_data in data["users"]:
            try:
                # 检查用户是否已存在
                existing_user = db.fetch_one("SELECT 1 FROM users WHERE id = ?", (user_data["id"],))
                if existing_user:
                    logger.info(f"用户 {user_data['username']} 已存在，跳过")
                    continue
                
                # 处理日期时间格式
                created_at = user_data.get("created_at")
                if isinstance(created_at, str):
                    try:
                        # 尝试解析各种日期格式
                        created_at = datetime.fromisoformat(created_at)
                    except ValueError:
                        try:
                            created_at = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S.%f")
                        except ValueError:
                            created_at = datetime.now()
                    
                    created_at = created_at.isoformat()
                else:
                    created_at = datetime.now().isoformat()
                
                # 处理最后登录时间
                last_login = user_data.get("last_login")
                if last_login:
                    try:
                        # 尝试解析各种日期格式
                        if isinstance(last_login, str):
                            try:
                                last_login = datetime.fromisoformat(last_login)
                            except ValueError:
                                try:
                                    last_login = datetime.strptime(last_login, "%Y-%m-%d %H:%M:%S.%f")
                                except ValueError:
                                    last_login = None
                            
                            if last_login:
                                last_login = last_login.isoformat()
                    except Exception:
                        last_login = None
                
                # 处理活跃状态
                is_active = 1 if user_data.get("is_active", True) else 0
                
                # 准备插入数据
                db_user_data = {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"],
                    "hashed_password": user_data["hashed_password"],
                    "role": user_data.get("role", UserRole.USER),
                    "created_at": created_at,
                    "last_login": last_login,
                    "is_active": is_active
                }
                
                # 插入数据库
                db.insert("users", db_user_data)
                migrated_users += 1
                logger.info(f"已迁移用户 {user_data['username']}")
                
            except Exception as e:
                logger.error(f"迁移用户 {user_data.get('username', '未知')} 失败: {e}")
        
        logger.info(f"用户数据迁移完成，成功迁移 {migrated_users}/{total_users} 个用户")
        
        # 备份JSON文件
        backup_file = f"{json_file}.bak"
        os.rename(json_file, backup_file)
        logger.info(f"原JSON文件已备份为 {backup_file}")
        
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")

if __name__ == "__main__":
    migrate_users_from_json_to_sqlite() 