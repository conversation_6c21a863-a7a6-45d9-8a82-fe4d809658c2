import os
import time
import logging
import tempfile
import shutil
import subprocess
import pyautogui
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入win32相关模块，采用可靠的方式
try:
    import win32api
    import win32con
    import win32gui
    HAS_WIN32 = True
except ImportError:
    logger.warning("无法导入win32模块，将使用模拟模式")
    HAS_WIN32 = False

# Vector Magic 程序路径配置
# 默认路径列表，按优先级排序
DEFAULT_VECTOR_MAGIC_PATHS = [
    r"C:\Program Files (x86)\Vector Magic\vmde.exe",  # 实际的可执行文件
    r"C:\Program Files\Vector Magic\vmde.exe",
    r"C:\Program Files (x86)\Vector Magic\VectorMagic.exe",  # 旧版本路径
    r"C:\Program Files\Vector Magic\VectorMagic.exe"
]

# 从配置文件或环境变量读取路径
def get_vector_magic_path():
    """获取Vector Magic可执行文件路径"""
    import os

    # 首先尝试从环境变量获取
    env_path = os.getenv('VECTOR_MAGIC_PATH')
    if env_path and os.path.exists(env_path):
        return env_path

    # 尝试从配置文件读取
    config_file = 'vector_magic_config.txt'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_path = f.read().strip()
                if config_path and os.path.exists(config_path):
                    return config_path
        except Exception as e:
            logger.warning(f"读取配置文件失败: {e}")

    # 尝试默认路径
    for path in DEFAULT_VECTOR_MAGIC_PATHS:
        if os.path.exists(path):
            logger.info(f"找到Vector Magic可执行文件: {path}")
            return path

    return None

# 获取实际的Vector Magic路径
VECTOR_MAGIC_PATH = get_vector_magic_path()

# 输出目录
VECTOR_OUTPUT_DIR = "vector_outputs"
os.makedirs(VECTOR_OUTPUT_DIR, exist_ok=True)

class VectorizeService:
    """矢量图生成服务类"""

    @staticmethod
    def set_vector_magic_path(new_path):
        """设置Vector Magic可执行文件路径（管理员功能）"""
        try:
            if not os.path.exists(new_path):
                return False, f"指定的路径不存在: {new_path}"

            if not new_path.lower().endswith(('.exe', '.app')):
                return False, "指定的文件不是可执行文件"

            # 保存到配置文件
            config_file = 'vector_magic_config.txt'
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_path)

            # 更新全局变量
            global VECTOR_MAGIC_PATH
            VECTOR_MAGIC_PATH = new_path

            logger.info(f"Vector Magic路径已更新为: {new_path}")
            return True, f"Vector Magic路径已成功设置为: {new_path}"

        except Exception as e:
            logger.error(f"设置Vector Magic路径时出错: {e}")
            return False, f"设置路径时出错: {str(e)}"

    @staticmethod
    def get_current_path():
        """获取当前Vector Magic路径"""
        return VECTOR_MAGIC_PATH

    @staticmethod
    def check_installation():
        """检查Vector Magic安装状态"""
        current_path = VectorizeService.get_current_path()
        if not current_path:
            return False, "未找到Vector Magic可执行文件，请设置正确的安装路径"

        if not os.path.exists(current_path):
            return False, f"Vector Magic路径无效: {current_path}"

        return True, f"Vector Magic已正确安装: {current_path}"

    @staticmethod
    def get_system_status():
        """获取完整的系统状态信息"""
        status = {
            "vector_magic": {
                "installed": False,
                "path": None,
                "message": ""
            },
            "dependencies": {
                "win32": HAS_WIN32,
                "pyautogui": False
            },
            "system": {
                "platform": os.name,
                "python_version": None
            }
        }

        # 检查Vector Magic
        is_installed, message = VectorizeService.check_installation()
        status["vector_magic"]["installed"] = is_installed
        status["vector_magic"]["path"] = VectorizeService.get_current_path()
        status["vector_magic"]["message"] = message

        # 检查PyAutoGUI
        try:
            import pyautogui
            status["dependencies"]["pyautogui"] = True
        except ImportError:
            status["dependencies"]["pyautogui"] = False

        # 检查Python版本
        import sys
        status["system"]["python_version"] = sys.version

        return status

    @staticmethod
    def vectorize_image(image_path, quality="medium"):
        """
        使用Vector Magic将位图转换为矢量图
        
        参数:
        - image_path: 图片文件路径
        - quality: 矢量质量，可选值: low, medium, high
        
        返回:
        - 矢量图文件路径字典，包含SVG、PNG和预览图路径
        """
        try:
            # 检查图片是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return None, "图片文件不存在"
                
            # 检查Vector Magic是否已安装
            if not VECTOR_MAGIC_PATH:
                logger.error("未找到Vector Magic可执行文件，请管理员设置正确的安装路径")
                return None, "未找到Vector Magic可执行文件，请管理员设置正确的安装路径"

            if not os.path.exists(VECTOR_MAGIC_PATH):
                logger.error(f"Vector Magic路径无效: {VECTOR_MAGIC_PATH}")
                return None, f"Vector Magic路径无效: {VECTOR_MAGIC_PATH}"
                
            # 检查win32模块是否可用
            if not HAS_WIN32:
                logger.error("win32模块不可用，无法执行矢量图转换")
                return None, "系统缺少必要的win32模块，无法执行矢量图转换"
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            base_filename = f"{timestamp}_{unique_id}"
            
            # 准备输出文件路径
            output_svg_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.svg")
            output_png_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.png")
            preview_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}_preview.png")
            
            # 启动Vector Magic
            logger.info(f"启动Vector Magic处理图片: {image_path}")
            proc = subprocess.Popen([VECTOR_MAGIC_PATH, image_path])

            # 延长等待时间，确保程序完全启动
            logger.info("等待Vector Magic完全启动...")
            vector_window = VectorizeService._wait_for_vector_magic_window(timeout=60)
            if not vector_window:
                logger.error("无法找到Vector Magic窗口")
                try:
                    proc.terminate()
                except:
                    pass
                return None, "无法找到Vector Magic窗口，请检查Vector Magic是否正确安装"

            # 激活窗口并等待界面加载完成
            logger.info("激活Vector Magic窗口")
            win32gui.SetForegroundWindow(vector_window)
            time.sleep(3)  # 增加等待时间

            # 等待界面完全加载
            logger.info("等待界面完全加载")
            VectorizeService._wait_for_interface_ready(timeout=15)

            # 执行Vector Magic工作流程
            logger.info("开始执行Vector Magic工作流程")
            workflow_success = VectorizeService._execute_vector_magic_workflow(quality)
            if not workflow_success:
                logger.error("Vector Magic工作流程执行失败")
                VectorizeService._safe_close_vector_magic(vector_window, proc)
                return None, "Vector Magic工作流程执行失败"

            # 等待额外时间确保所有处理完成
            logger.info("等待最终处理完成...")
            time.sleep(5)

            # 截图当前结果作为预览图
            logger.info("截取预览图")
            try:
                pyautogui.screenshot(preview_path)
            except Exception as e:
                logger.warning(f"截取预览图失败: {e}")

            # 检查生成的文件
            generated_files = VectorizeService._check_generated_files()
            if generated_files:
                logger.info(f"检测到生成的文件: {generated_files}")

                # 将生成的文件复制到指定位置
                result = VectorizeService._organize_output_files(generated_files, base_filename)
            else:
                logger.warning("未检测到生成的文件，使用传统保存方法")
                # 使用传统保存方法作为备用
                save_success = VectorizeService._save_results(output_svg_path, output_png_path)
                if save_success:
                    result = {
                        "svg_file": os.path.basename(output_svg_path),
                        "png_file": os.path.basename(output_png_path),
                        "preview_file": os.path.basename(preview_path)
                    }
                else:
                    logger.error("传统保存方法也失败")
                    VectorizeService._safe_close_vector_magic(vector_window, proc)
                    return None, "文件保存失败"

            # 确保所有操作完成后再关闭Vector Magic
            logger.info("所有处理完成，准备关闭Vector Magic")
            time.sleep(2)  # 最后的等待时间
            VectorizeService._safe_close_vector_magic(vector_window, proc)
            
            # 检查最终结果
            if result and (result.get("svg_file") or result.get("png_file")):
                logger.info(f"矢量图生成成功: {result}")
                return result, None
            else:
                logger.error("矢量图文件未成功创建")
                return None, "矢量图文件未成功创建"
            
        except Exception as e:
            error_msg = f"矢量图生成过程中发生错误: {str(e)}"
            logger.exception(error_msg)

            # 尝试清理资源
            try:
                if 'proc' in locals():
                    proc.terminate()
                if 'vector_window' in locals() and vector_window:
                    win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
            except:
                pass

            # 提供更详细的错误信息
            if "找不到指定的文件" in str(e) or "No such file" in str(e):
                error_msg = "Vector Magic可执行文件未找到，请检查安装路径配置"
            elif "Access is denied" in str(e) or "权限" in str(e):
                error_msg = "权限不足，请以管理员身份运行或检查文件权限"
            elif "win32" in str(e).lower():
                error_msg = "Win32模块错误，请确保已正确安装pywin32"

            return None, error_msg
    
    @staticmethod
    def _wait_for_vector_magic_window(timeout=30):
        """等待Vector Magic窗口出现"""
        start_time = time.time()
        retry_count = 0
        max_retries = 3

        while time.time() - start_time < timeout:
            try:
                vector_window = None

                # 使用更安全的窗口枚举方法
                def find_vector_magic_window(hwnd, ctx):
                    try:
                        if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
                            window_text = win32gui.GetWindowText(hwnd)
                            if window_text and ("Vector Magic" in window_text or "vmde" in window_text.lower()):
                                nonlocal vector_window
                                vector_window = hwnd
                                return False
                    except Exception as e:
                        # 忽略单个窗口的错误，继续枚举
                        pass
                    return True

                # 尝试枚举窗口
                try:
                    win32gui.EnumWindows(find_vector_magic_window, None)
                    retry_count = 0  # 重置重试计数
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"枚举窗口时出错 (尝试 {retry_count}/{max_retries}): {e}")

                    if retry_count >= max_retries:
                        logger.error("多次枚举窗口失败，尝试替代方法")
                        # 使用替代方法查找窗口
                        vector_window = VectorizeService._find_window_alternative()
                        if vector_window:
                            break
                        retry_count = 0  # 重置计数继续尝试

                    time.sleep(1)  # 等待更长时间再重试
                    continue

                if vector_window:
                    try:
                        window_title = win32gui.GetWindowText(vector_window)
                        logger.info(f"找到Vector Magic窗口: {window_title}")
                        return vector_window
                    except:
                        logger.info(f"找到Vector Magic窗口: {vector_window}")
                        return vector_window

            except Exception as e:
                logger.warning(f"查找窗口时出错: {e}")

            time.sleep(0.5)

        logger.warning(f"在{timeout}秒内未找到Vector Magic窗口")
        return None

    @staticmethod
    def _find_window_alternative():
        """替代的窗口查找方法"""
        try:
            # 方法1: 使用FindWindow API
            try:
                hwnd = win32gui.FindWindow(None, "Vector Magic")
                if hwnd:
                    return hwnd
            except:
                pass

            # 方法2: 通过进程名查找
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'vmde' in proc.info['name'].lower():
                        # 找到进程，尝试获取其窗口
                        pid = proc.info['pid']
                        def enum_windows_proc(hwnd, lParam):
                            if win32gui.IsWindow(hwnd):
                                _, found_pid = win32gui.GetWindowThreadProcessId(hwnd)
                                if found_pid == pid:
                                    return hwnd
                            return True

                        result = win32gui.EnumWindows(enum_windows_proc, None)
                        if result and result != True:
                            return result
            except ImportError:
                pass
            except Exception as e:
                logger.warning(f"通过进程查找窗口失败: {e}")

            return None

        except Exception as e:
            logger.warning(f"替代窗口查找方法失败: {e}")
            return None

    @staticmethod
    def _get_vector_magic_window():
        """获取当前的Vector Magic窗口句柄"""
        try:
            vector_window = None

            def find_vector_magic_window(hwnd, ctx):
                try:
                    if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if window_text and ("Vector Magic" in window_text or "vmde" in window_text.lower()):
                            nonlocal vector_window
                            vector_window = hwnd
                            return False
                except:
                    pass
                return True

            try:
                win32gui.EnumWindows(find_vector_magic_window, None)
            except Exception as e:
                logger.warning(f"枚举窗口时出错，尝试替代方法: {e}")
                vector_window = VectorizeService._find_window_alternative()

            return vector_window

        except Exception as e:
            logger.error(f"获取Vector Magic窗口时出错: {e}")
            return None

    @staticmethod
    def _wait_for_interface_ready(timeout=15):
        """等待界面准备就绪"""
        logger.info("等待Vector Magic界面加载完成...")
        start_time = time.time()

        # 基本等待时间
        time.sleep(5)

        # 检查界面是否准备就绪
        while time.time() - start_time < timeout:
            try:
                # 检查窗口是否响应
                vector_window = VectorizeService._get_vector_magic_window()
                if vector_window:
                    # 检查窗口是否可见且响应
                    if win32gui.IsWindowVisible(vector_window):
                        logger.info("Vector Magic界面已准备就绪")
                        return True

                time.sleep(1)

            except Exception as e:
                logger.warning(f"检查界面状态时出错: {e}")
                time.sleep(1)

        logger.warning("等待界面准备就绪超时，但继续执行")
        return True  # 即使超时也继续执行

    @staticmethod
    def _execute_vector_magic_workflow(quality="medium"):
        """执行Vector Magic工作流程"""
        try:
            logger.info(f"开始执行Vector Magic工作流程，质量级别: {quality}")

            # 根据质量级别执行不同的操作序列
            if quality == "low":
                return VectorizeService._execute_basic_workflow()
            elif quality == "high":
                return VectorizeService._execute_advanced_workflow()
            else:  # medium
                return VectorizeService._execute_medium_workflow()

        except Exception as e:
            logger.error(f"执行Vector Magic工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_basic_workflow():
        """执行基本工作流程"""
        try:
            logger.info("执行基本工作流程")

            # 方案1：图像识别点击
            if VectorizeService._click_button_by_image("auto_button"):
                logger.info("通过图像识别成功点击自动按钮")
            else:
                # 方案2：文本识别点击
                if VectorizeService._click_button_by_text("Auto"):
                    logger.info("通过文本识别成功点击自动按钮")
                else:
                    # 方案3：坐标点击（备用）
                    logger.info("使用坐标点击作为备用方案")
                    screen_width, screen_height = pyautogui.size()
                    auto_button_x = int(screen_width * 0.3)
                    auto_button_y = int(screen_height * 0.25)
                    pyautogui.click(auto_button_x, auto_button_y)

            time.sleep(2)

            # 等待处理完成
            VectorizeService._wait_for_processing_complete()

            return True

        except Exception as e:
            logger.error(f"执行基本工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_medium_workflow():
        """执行中等质量工作流程"""
        try:
            logger.info("执行中等质量工作流程")

            # 获取Vector Magic窗口句柄
            vector_window = VectorizeService._get_vector_magic_window()
            if not vector_window:
                logger.error("无法获取Vector Magic窗口句柄")
                return False

            # 使用智能按钮检测
            process_keywords = ["Process", "处理", "Convert", "转换", "Go", "Start"]
            if VectorizeService._smart_button_detection(vector_window, process_keywords):
                logger.info("成功点击处理按钮")
            else:
                # 备用方案：坐标点击
                logger.info("使用坐标点击作为备用方案")
                screen_width, screen_height = pyautogui.size()
                process_button_x = int(screen_width * 0.5)
                process_button_y = int(screen_height * 0.4)
                pyautogui.click(process_button_x, process_button_y)

            time.sleep(2)

            # 等待处理完成
            VectorizeService._wait_for_processing_complete()

            return True

        except Exception as e:
            logger.error(f"执行中等质量工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_advanced_workflow():
        """执行高级工作流程 - 点击高级模式 → 连续下一步直到出现保存按钮 → 点击保存"""
        try:
            logger.info("开始执行高级工作流程")

            # 获取Vector Magic窗口句柄
            vector_window = VectorizeService._get_vector_magic_window()
            if not vector_window:
                logger.error("无法获取Vector Magic窗口句柄")
                return False

            # 步骤1: 点击高级模式按钮
            logger.info("步骤1: 点击高级模式按钮")
            if not VectorizeService._click_advanced_button(vector_window):
                logger.error("点击高级模式按钮失败")
                return False

            # 等待界面响应
            time.sleep(3)

            # 步骤2: 连续点击"下一步"直到出现"快速保存"按钮
            logger.info("步骤2: 连续点击下一步，直到出现快速保存按钮")
            max_next_clicks = 10  # 最大点击次数，防止无限循环
            next_click_count = 0

            while next_click_count < max_next_clicks:
                next_click_count += 1
                logger.info(f"第{next_click_count}次点击下一步")

                # 检查是否已经出现快速保存按钮
                if VectorizeService._check_for_save_button(vector_window):
                    logger.info(f"✓ 在第{next_click_count}次点击后检测到快速保存按钮")
                    break

                # 点击下一步按钮
                if not VectorizeService._click_next_button(vector_window):
                    logger.warning(f"第{next_click_count}次点击下一步失败")
                    # 即使失败也继续，可能是界面变化导致的

                # 每次点击之间的时间间隔
                time.sleep(2)

                # 再次检查是否出现保存按钮
                if VectorizeService._check_for_save_button(vector_window):
                    logger.info(f"✓ 在第{next_click_count}次点击后检测到快速保存按钮")
                    break

            # 检查是否成功找到保存按钮
            if not VectorizeService._check_for_save_button(vector_window):
                logger.warning(f"连续点击{max_next_clicks}次下一步后仍未找到快速保存按钮，尝试继续")

            # 步骤3: 点击快速保存按钮
            logger.info("步骤3: 点击快速保存按钮")
            if not VectorizeService._click_save_button(vector_window):
                logger.error("点击快速保存按钮失败")
                return False

            # 等待保存完成
            logger.info("等待保存操作完成...")
            time.sleep(3)

            logger.info("✓ 高级工作流程执行完成")
            return True

        except Exception as e:
            logger.error(f"执行高级工作流程时出错: {e}")
            return False

    @staticmethod
    def _wait_for_processing_complete(timeout=60):
        """等待处理完成（基础版本）"""
        logger.info("等待Vector Magic处理完成...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 这里可以添加检测处理完成的逻辑
            # 例如检测特定的UI元素或窗口标题变化
            time.sleep(1)

            # 简单的时间等待，实际应用中可以改进为更智能的检测
            if time.time() - start_time > 15:  # 假设15秒后处理完成
                logger.info("Vector Magic处理完成")
                return True

        logger.warning("等待处理完成超时")
        return False

    @staticmethod
    def _wait_for_processing_complete_advanced(timeout=180):
        """等待高级处理完成（更长时间，更智能检测）"""
        logger.info("等待Vector Magic高级处理完成...")
        start_time = time.time()
        last_check_time = start_time

        while time.time() - start_time < timeout:
            current_time = time.time()

            # 每10秒检查一次状态
            if current_time - last_check_time >= 10:
                logger.info(f"处理中... 已等待 {int(current_time - start_time)} 秒")

                # 检查是否有进度指示器或状态变化
                if VectorizeService._check_processing_status():
                    logger.info("检测到处理完成信号")
                    return True

                last_check_time = current_time

            time.sleep(2)

        logger.warning(f"等待处理完成超时 ({timeout}秒)")
        return False

    @staticmethod
    def _check_processing_status():
        """检查处理状态"""
        try:
            # 方法1: 检查窗口标题变化
            vector_window = VectorizeService._get_vector_magic_window()
            if vector_window:
                window_title = win32gui.GetWindowText(vector_window)
                # 如果标题包含"完成"、"Done"等词汇，说明处理完成
                completion_indicators = ["完成", "Done", "Complete", "Finished", "Ready"]
                for indicator in completion_indicators:
                    if indicator.lower() in window_title.lower():
                        return True

            # 方法2: 检查是否出现保存相关按钮
            save_keywords = ["Save", "保存", "Export", "导出", "Finish", "完成"]
            if vector_window and VectorizeService._detect_buttons_in_window(vector_window, save_keywords):
                return True

            # 方法3: 检查CPU使用率（简化版本）
            # 在实际应用中可以监控Vector Magic进程的CPU使用率

            return False

        except Exception as e:
            logger.warning(f"检查处理状态时出错: {e}")
            return False

    @staticmethod
    def _detect_buttons_in_window(window_handle, keywords):
        """在窗口中检测特定按钮"""
        try:
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=window_region)

            # 尝试OCR检测
            try:
                import pytesseract
                ocr_data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)

                for i, text in enumerate(ocr_data['text']):
                    if text.strip() and int(ocr_data['conf'][i]) > 60:
                        for keyword in keywords:
                            if keyword.lower() in text.lower():
                                return True
            except:
                pass

            return False

        except Exception as e:
            logger.warning(f"检测窗口按钮时出错: {e}")
            return False

    @staticmethod
    def _execute_quick_save(window_handle):
        """执行快速保存流程"""
        try:
            logger.info("开始执行快速保存流程")

            # 步骤1: 点击快速保存或保存按钮
            save_keywords = ["Quick Save", "快速保存", "Save", "保存", "Export", "导出"]
            if VectorizeService._smart_button_detection(window_handle, save_keywords):
                logger.info("✓ 成功点击保存按钮")
            else:
                # 尝试快捷键
                logger.info("尝试使用Ctrl+S快捷键")
                pyautogui.hotkey('ctrl', 's')

            time.sleep(2)

            # 步骤2: 处理保存对话框
            return VectorizeService._handle_save_dialog()

        except Exception as e:
            logger.error(f"执行快速保存时出错: {e}")
            return False

    @staticmethod
    def _handle_save_dialog():
        """处理保存对话框"""
        try:
            logger.info("处理保存对话框")

            # 等待保存对话框出现
            time.sleep(2)

            # 确保输出目录存在
            output_dir = os.path.abspath(VECTOR_OUTPUT_DIR)
            os.makedirs(output_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            base_filename = f"vector_{timestamp}_{unique_id}"

            # 设置保存路径（使用绝对路径确保浏览器能访问）
            svg_path = os.path.join(output_dir, f"{base_filename}.svg")

            # 清除现有路径并输入新路径
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.5)
            pyautogui.write(svg_path)  # 输入完整路径
            time.sleep(1)

            # 确认保存
            pyautogui.press('enter')
            time.sleep(3)

            # 检查文件是否成功保存
            if os.path.exists(svg_path):
                logger.info(f"✓ SVG文件保存成功: {svg_path}")

                # 尝试保存PNG版本
                VectorizeService._save_png_version(base_filename, output_dir)

                return True
            else:
                logger.warning(f"SVG文件保存可能失败: {svg_path}")
                return False

        except Exception as e:
            logger.error(f"处理保存对话框时出错: {e}")
            return False

    @staticmethod
    def _save_png_version(base_filename, output_dir):
        """保存PNG版本"""
        try:
            logger.info("尝试保存PNG版本")

            # 尝试导出PNG
            time.sleep(1)

            # 方法1: 使用菜单导出
            pyautogui.hotkey('alt', 'f')  # 文件菜单
            time.sleep(0.5)

            # 查找导出选项
            export_keys = ['e', 'x', 'p']  # 常见的导出快捷键
            for key in export_keys:
                pyautogui.press(key)
                time.sleep(0.5)

                # 检查是否打开了导出对话框
                if VectorizeService._check_export_dialog():
                    break

            # 设置PNG路径
            png_path = os.path.join(output_dir, f"{base_filename}.png")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)
            pyautogui.write(png_path)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)

            if os.path.exists(png_path):
                logger.info(f"✓ PNG文件保存成功: {png_path}")
            else:
                logger.warning("PNG文件保存可能失败")

        except Exception as e:
            logger.warning(f"保存PNG版本时出错: {e}")

    @staticmethod
    def _check_export_dialog():
        """检查是否打开了导出对话框"""
        try:
            # 简单检查：截图并查找"导出"、"Export"等关键词
            screenshot = pyautogui.screenshot()

            try:
                import pytesseract
                text = pytesseract.image_to_string(screenshot)
                export_indicators = ["Export", "导出", "Save as", "另存为"]
                for indicator in export_indicators:
                    if indicator.lower() in text.lower():
                        return True
            except:
                pass

            return False

        except:
            return False

    @staticmethod
    def _save_results(output_svg_path, output_png_path):
        """保存结果文件（旧版本，保留兼容性）"""
        try:
            logger.info("开始保存结果文件")

            # 保存SVG文件
            logger.info("保存SVG文件")
            pyautogui.hotkey('ctrl', 's')  # 或者使用菜单
            time.sleep(1)

            # 输入文件名
            pyautogui.write(output_svg_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)

            # 保存PNG文件（如果需要）
            logger.info("导出PNG文件")
            pyautogui.hotkey('ctrl', 'e')  # 或者使用导出菜单
            time.sleep(1)

            # 输入文件名
            pyautogui.write(output_png_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)

            return True

        except Exception as e:
            logger.error(f"保存结果文件时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_image(button_name, confidence=0.8):
        """通过图像识别点击按钮"""
        try:
            # 按钮图片路径
            button_images = {
                "auto_button": "button_images/auto_button.png",
                "process_button": "button_images/process_button.png",
                "advanced_button": "button_images/advanced_button.png",
                "save_button": "button_images/save_button.png",
                "export_button": "button_images/export_button.png"
            }

            if button_name not in button_images:
                logger.warning(f"未找到按钮图片配置: {button_name}")
                return False

            image_path = button_images[button_name]

            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                logger.warning(f"按钮图片文件不存在: {image_path}")
                return False

            # 在屏幕上查找按钮
            try:
                button_location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                if button_location:
                    # 点击按钮中心
                    button_center = pyautogui.center(button_location)
                    pyautogui.click(button_center)
                    logger.info(f"成功通过图像识别点击按钮: {button_name} at {button_center}")
                    return True
                else:
                    logger.warning(f"在屏幕上未找到按钮图像: {button_name}")
                    return False
            except pyautogui.ImageNotFoundException:
                logger.warning(f"图像识别失败: {button_name}")
                return False

        except Exception as e:
            logger.error(f"图像识别点击按钮时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_text(text, region=None):
        """通过OCR文本识别点击按钮"""
        try:
            # 需要安装pytesseract和Pillow
            try:
                import pytesseract
                from PIL import Image
            except ImportError:
                logger.warning("OCR功能需要安装pytesseract和Pillow")
                return False

            # 截取屏幕
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()

            # 使用OCR识别文本
            ocr_data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)

            # 查找匹配的文本
            for i, detected_text in enumerate(ocr_data['text']):
                if text.lower() in detected_text.lower() and int(ocr_data['conf'][i]) > 50:
                    # 计算文本位置
                    x = ocr_data['left'][i] + ocr_data['width'][i] // 2
                    y = ocr_data['top'][i] + ocr_data['height'][i] // 2

                    # 如果指定了区域，需要调整坐标
                    if region:
                        x += region[0]
                        y += region[1]

                    pyautogui.click(x, y)
                    logger.info(f"成功通过文本识别点击按钮: {text} at ({x}, {y})")
                    return True

            logger.warning(f"未找到文本: {text}")
            return False

        except Exception as e:
            logger.error(f"文本识别点击按钮时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_window_control(window_handle, control_id):
        """通过Windows控件ID点击按钮"""
        try:
            # 查找子窗口控件
            def enum_child_windows(hwnd, results):
                results.append(hwnd)
                return True

            child_windows = []
            win32gui.EnumChildWindows(window_handle, enum_child_windows, child_windows)

            for child_hwnd in child_windows:
                # 获取控件ID
                try:
                    ctrl_id = win32gui.GetDlgCtrlID(child_hwnd)
                    if ctrl_id == control_id:
                        # 发送点击消息
                        win32gui.PostMessage(child_hwnd, win32con.BM_CLICK, 0, 0)
                        logger.info(f"成功通过控件ID点击按钮: {control_id}")
                        return True
                except:
                    continue

            logger.warning(f"未找到控件ID: {control_id}")
            return False

        except Exception as e:
            logger.error(f"控件点击时出错: {e}")
            return False

    @staticmethod
    def _click_advanced_button(window_handle):
        """点击高级模式按钮"""
        try:
            logger.info("尝试点击高级模式按钮")

            # 方法1: 智能检测高级按钮
            advanced_keywords = ["Advanced", "高级", "Expert", "专家", "Manual", "手动"]
            if VectorizeService._smart_button_detection(window_handle, advanced_keywords):
                logger.info("✓ 通过智能检测成功点击高级按钮")
                return True

            # 方法2: 图像识别
            if VectorizeService._click_button_by_image("advanced_button"):
                logger.info("✓ 通过图像识别成功点击高级按钮")
                return True

            # 方法3: 文本识别
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            for keyword in advanced_keywords:
                if VectorizeService._click_button_by_text(keyword, region=window_region):
                    logger.info(f"✓ 通过文本识别成功点击高级按钮: {keyword}")
                    return True

            # 方法4: 坐标点击（备用方案）
            logger.info("使用坐标点击高级按钮作为备用方案")
            screen_width, screen_height = pyautogui.size()

            # 尝试多个可能的高级按钮位置
            advanced_positions = [
                (int(screen_width * 0.3), int(screen_height * 0.5)),   # 左侧中部
                (int(screen_width * 0.2), int(screen_height * 0.6)),   # 左侧下部
                (int(screen_width * 0.4), int(screen_height * 0.4)),   # 中部偏左
            ]

            for x, y in advanced_positions:
                logger.info(f"尝试点击位置: ({x}, {y})")
                pyautogui.click(x, y)
                time.sleep(1)

                # 检查是否成功（简单检查：看是否有界面变化）
                if VectorizeService._check_interface_change():
                    logger.info("✓ 坐标点击高级按钮成功")
                    return True

            logger.warning("所有方法都无法点击高级按钮")
            return False

        except Exception as e:
            logger.error(f"点击高级按钮时出错: {e}")
            return False

    @staticmethod
    def _click_next_button(window_handle):
        """点击下一步按钮"""
        try:
            # 方法1: 智能检测下一步按钮
            next_keywords = ["Next", "下一步", "Continue", "继续", "Forward", "前进", ">"]
            if VectorizeService._smart_button_detection(window_handle, next_keywords):
                logger.info("✓ 通过智能检测成功点击下一步按钮")
                return True

            # 方法2: 图像识别
            if VectorizeService._click_button_by_image("next_button"):
                logger.info("✓ 通过图像识别成功点击下一步按钮")
                return True

            # 方法3: 文本识别
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            for keyword in next_keywords:
                if VectorizeService._click_button_by_text(keyword, region=window_region):
                    logger.info(f"✓ 通过文本识别成功点击下一步按钮: {keyword}")
                    return True

            # 方法4: 键盘快捷键
            logger.info("尝试使用键盘快捷键")
            pyautogui.press('enter')  # 很多对话框的默认按钮
            time.sleep(0.5)

            # 或者尝试Tab + Enter
            pyautogui.press('tab')
            time.sleep(0.2)
            pyautogui.press('enter')

            logger.info("✓ 使用键盘快捷键点击下一步")
            return True

        except Exception as e:
            logger.error(f"点击下一步按钮时出错: {e}")
            return False

    @staticmethod
    def _check_for_save_button(window_handle):
        """检查是否出现了快速保存按钮"""
        try:
            logger.info("检查是否出现快速保存按钮")

            # 获取窗口区域
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            # 定义快速保存相关的关键词
            save_keywords = [
                "Quick Save", "快速保存", "Save", "保存",
                "Export", "导出", "Finish", "完成",
                "Done", "结束", "Save As", "另存为"
            ]

            # 方法1: 图像识别检测保存按钮
            save_button_images = ["save_button", "quick_save_button", "export_button", "finish_button"]
            for button_image in save_button_images:
                try:
                    if VectorizeService._detect_button_image(button_image):
                        logger.info(f"✓ 通过图像识别检测到保存按钮: {button_image}")
                        return True
                except:
                    continue

            # 方法2: 文本识别检测保存按钮
            try:
                import pytesseract
                from PIL import Image

                # 截取窗口区域
                screenshot = pyautogui.screenshot(region=window_region)

                # OCR识别文本
                ocr_data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)

                for i, text in enumerate(ocr_data['text']):
                    if text.strip() and int(ocr_data['conf'][i]) > 50:
                        for keyword in save_keywords:
                            if keyword.lower() in text.lower():
                                logger.info(f"✓ 通过文本识别检测到保存按钮: '{text}' (匹配关键词: {keyword})")
                                return True

            except ImportError:
                logger.debug("pytesseract未安装，跳过OCR检测")
            except Exception as e:
                logger.debug(f"OCR检测失败: {e}")

            # 方法3: 检查窗口标题变化
            try:
                window_title = win32gui.GetWindowText(window_handle)
                title_indicators = ["save", "保存", "export", "导出", "finish", "完成"]
                for indicator in title_indicators:
                    if indicator.lower() in window_title.lower():
                        logger.info(f"✓ 通过窗口标题检测到保存状态: '{window_title}'")
                        return True
            except:
                pass

            # 方法4: 颜色检测（检测保存按钮的特定颜色）
            try:
                if VectorizeService._detect_save_button_by_color(window_region):
                    logger.info("✓ 通过颜色检测到保存按钮")
                    return True
            except Exception as e:
                logger.debug(f"颜色检测失败: {e}")

            logger.debug("未检测到快速保存按钮")
            return False

        except Exception as e:
            logger.error(f"检查快速保存按钮时出错: {e}")
            return False

    @staticmethod
    def _detect_button_image(button_name):
        """检测特定按钮图像是否存在"""
        try:
            import os
            image_path = f"button_images/{button_name}.png"
            if os.path.exists(image_path):
                location = pyautogui.locateOnScreen(image_path, confidence=0.8)
                return location is not None
            return False
        except:
            return False

    @staticmethod
    def _detect_save_button_by_color(window_region):
        """通过颜色检测保存按钮"""
        try:
            import cv2
            import numpy as np

            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=window_region)
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)

            # 定义保存按钮常见的颜色范围
            save_button_colors = [
                # 绿色保存按钮
                [(40, 50, 50), (80, 255, 255)],
                # 蓝色保存按钮
                [(100, 50, 50), (130, 255, 255)],
                # 橙色/黄色保存按钮
                [(10, 50, 50), (30, 255, 255)]
            ]

            for lower, upper in save_button_colors:
                lower = np.array(lower)
                upper = np.array(upper)

                mask = cv2.inRange(hsv, lower, upper)
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 800:  # 保存按钮通常比较大
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h
                        if 1.5 <= aspect_ratio <= 4.0:  # 保存按钮的长宽比
                            return True

            return False

        except ImportError:
            return False
        except Exception as e:
            logger.debug(f"颜色检测保存按钮失败: {e}")
            return False

    @staticmethod
    def _click_save_button(window_handle):
        """点击快速保存按钮"""
        try:
            logger.info("尝试点击快速保存按钮")

            # 获取窗口区域
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            # 方法1: 智能检测保存按钮
            save_keywords = [
                "Quick Save", "快速保存", "Save", "保存",
                "Export", "导出", "Finish", "完成", "Done", "结束"
            ]

            if VectorizeService._smart_button_detection(window_handle, save_keywords):
                logger.info("✓ 通过智能检测成功点击保存按钮")
                return True

            # 方法2: 图像识别点击
            save_button_images = ["save_button", "quick_save_button", "export_button", "finish_button"]
            for button_image in save_button_images:
                if VectorizeService._click_button_by_image(button_image):
                    logger.info(f"✓ 通过图像识别成功点击保存按钮: {button_image}")
                    return True

            # 方法3: 文本识别点击
            for keyword in save_keywords:
                if VectorizeService._click_button_by_text(keyword, region=window_region):
                    logger.info(f"✓ 通过文本识别成功点击保存按钮: {keyword}")
                    return True

            # 方法4: 键盘快捷键
            logger.info("尝试使用Ctrl+S快捷键保存")
            pyautogui.hotkey('ctrl', 's')
            time.sleep(1)

            # 方法5: 尝试Enter键（如果保存按钮是默认按钮）
            logger.info("尝试使用Enter键确认保存")
            pyautogui.press('enter')
            time.sleep(1)

            logger.info("✓ 使用键盘快捷键执行保存操作")
            return True

        except Exception as e:
            logger.error(f"点击保存按钮时出错: {e}")
            return False

    @staticmethod
    def _check_interface_change():
        """检查界面是否发生变化"""
        try:
            # 简单的界面变化检测
            # 可以通过截图对比、窗口标题变化等方式实现
            time.sleep(0.5)  # 等待界面更新
            return True  # 简化实现，假设总是有变化
        except:
            return False

    @staticmethod
    def _smart_button_detection(window_handle, button_keywords):
        """智能按钮检测 - 综合多种方法"""
        try:
            logger.info(f"开始智能按钮检测，关键词: {button_keywords}")

            # 获取窗口区域
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            # 方法1：尝试图像识别
            for keyword in button_keywords:
                if VectorizeService._click_button_by_image(f"{keyword}_button"):
                    return True

            # 方法2：尝试文本识别（在窗口区域内）
            for keyword in button_keywords:
                if VectorizeService._click_button_by_text(keyword, region=window_region):
                    return True

            # 方法3：尝试颜色识别（查找特定颜色的按钮）
            if VectorizeService._click_button_by_color(window_region, button_keywords):
                return True

            # 方法4：使用机器学习预测按钮位置
            if VectorizeService._click_button_by_ml_prediction(window_region, button_keywords):
                return True

            logger.warning("所有按钮检测方法都失败了")
            return False

        except Exception as e:
            logger.error(f"智能按钮检测时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_color(region, keywords):
        """通过颜色识别点击按钮"""
        try:
            import cv2
            import numpy as np

            # 截取指定区域
            screenshot = pyautogui.screenshot(region=region)
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # 定义常见按钮颜色范围（HSV）
            button_colors = {
                "blue": [(100, 50, 50), (130, 255, 255)],    # 蓝色按钮
                "green": [(40, 50, 50), (80, 255, 255)],     # 绿色按钮
                "red": [(0, 50, 50), (10, 255, 255)],        # 红色按钮
                "gray": [(0, 0, 50), (180, 30, 200)]         # 灰色按钮
            }

            hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)

            for color_name, (lower, upper) in button_colors.items():
                lower = np.array(lower)
                upper = np.array(upper)

                # 创建颜色掩码
                mask = cv2.inRange(hsv, lower, upper)

                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    # 过滤小的轮廓
                    area = cv2.contourArea(contour)
                    if area > 500:  # 按钮最小面积
                        # 获取边界框
                        x, y, w, h = cv2.boundingRect(contour)

                        # 检查长宽比（按钮通常是矩形）
                        aspect_ratio = w / h
                        if 0.5 <= aspect_ratio <= 4.0:
                            # 计算点击位置（相对于屏幕）
                            click_x = region[0] + x + w // 2
                            click_y = region[1] + y + h // 2

                            pyautogui.click(click_x, click_y)
                            logger.info(f"通过{color_name}颜色识别点击按钮 at ({click_x}, {click_y})")
                            return True

            return False

        except ImportError:
            logger.warning("颜色识别需要安装opencv-python")
            return False
        except Exception as e:
            logger.error(f"颜色识别点击时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_ml_prediction(region, keywords):
        """使用机器学习预测按钮位置"""
        try:
            # 这里可以集成预训练的按钮检测模型
            # 例如使用YOLO或其他目标检测模型

            # 简化版本：基于历史数据的位置预测
            button_history_file = "button_positions.json"

            if os.path.exists(button_history_file):
                import json
                with open(button_history_file, 'r') as f:
                    history = json.load(f)

                for keyword in keywords:
                    if keyword in history:
                        positions = history[keyword]
                        if positions:
                            # 使用最近成功的位置
                            last_pos = positions[-1]
                            click_x = region[0] + last_pos['x']
                            click_y = region[1] + last_pos['y']

                            pyautogui.click(click_x, click_y)
                            logger.info(f"通过历史位置预测点击按钮: {keyword} at ({click_x}, {click_y})")
                            return True

            return False

        except Exception as e:
            logger.error(f"ML预测点击时出错: {e}")
            return False

    @staticmethod
    def _record_successful_click(keyword, x, y, region):
        """记录成功的点击位置"""
        try:
            import json
            button_history_file = "button_positions.json"

            # 读取现有历史
            history = {}
            if os.path.exists(button_history_file):
                with open(button_history_file, 'r') as f:
                    history = json.load(f)

            # 添加新记录
            if keyword not in history:
                history[keyword] = []

            # 转换为相对坐标
            relative_x = x - region[0]
            relative_y = y - region[1]

            history[keyword].append({
                'x': relative_x,
                'y': relative_y,
                'timestamp': time.time()
            })

            # 只保留最近10次记录
            history[keyword] = history[keyword][-10:]

            # 保存历史
            with open(button_history_file, 'w') as f:
                json.dump(history, f, indent=2)

            logger.info(f"记录成功点击位置: {keyword} at ({relative_x}, {relative_y})")

        except Exception as e:
            logger.error(f"记录点击位置时出错: {e}")

    @staticmethod
    def _find_red_arrow_button():
        """
        查找红色箭头标注的按钮位置
        在实际应用中，这里应该使用计算机视觉技术如OpenCV来查找按钮
        这里简化为硬编码位置
        
        返回:
        - (x, y) 按钮中心坐标
        """
        # 这里使用图像中红色箭头指向的按钮的位置
        # 在实际应用中应该使用图像识别找到按钮
        screen_width, screen_height = pyautogui.size()
        # 假设按钮位于屏幕右侧的位置
        x = int(screen_width * 0.85)
        y = int(screen_height * 0.4)
        return (x, y)
    
    @staticmethod
    def _find_auto_button():
        """查找自动模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.25)
        return (x, y)
    
    @staticmethod
    def _find_advanced_button():
        """查找高级模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.5)
        return (x, y)

    @staticmethod
    def _safe_close_vector_magic(vector_window, proc):
        """安全关闭Vector Magic"""
        try:
            logger.info("安全关闭Vector Magic")

            # 方法1: 尝试正常关闭窗口
            if vector_window:
                try:
                    logger.info("尝试正常关闭窗口")
                    win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
                    time.sleep(3)  # 等待窗口关闭

                    # 检查窗口是否已关闭
                    if not win32gui.IsWindow(vector_window):
                        logger.info("✓ Vector Magic窗口已正常关闭")
                        return True
                except Exception as e:
                    logger.warning(f"正常关闭窗口失败: {e}")

            # 方法2: 尝试使用Alt+F4
            try:
                logger.info("尝试使用Alt+F4关闭")
                if vector_window:
                    win32gui.SetForegroundWindow(vector_window)
                    time.sleep(0.5)
                pyautogui.hotkey('alt', 'f4')
                time.sleep(3)

                if vector_window and not win32gui.IsWindow(vector_window):
                    logger.info("✓ 使用Alt+F4成功关闭")
                    return True
            except Exception as e:
                logger.warning(f"Alt+F4关闭失败: {e}")

            # 方法3: 强制终止进程
            try:
                logger.info("尝试强制终止进程")
                if proc:
                    proc.terminate()
                    proc.wait(timeout=5)
                    logger.info("✓ 进程已强制终止")
                    return True
            except Exception as e:
                logger.warning(f"强制终止进程失败: {e}")

            # 方法4: 使用taskkill命令
            try:
                logger.info("尝试使用taskkill命令")
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'vmde.exe'],
                             capture_output=True, timeout=10)
                subprocess.run(['taskkill', '/f', '/im', 'VectorMagic.exe'],
                             capture_output=True, timeout=10)
                logger.info("✓ 使用taskkill命令终止")
                return True
            except Exception as e:
                logger.warning(f"taskkill命令失败: {e}")

            logger.warning("所有关闭方法都失败了")
            return False

        except Exception as e:
            logger.error(f"安全关闭Vector Magic时出错: {e}")
            return False

    @staticmethod
    def _check_generated_files():
        """检查Vector Magic生成的文件"""
        try:
            logger.info("检查生成的文件")

            # 检查常见的输出位置
            possible_locations = [
                os.path.expanduser("~/Desktop"),  # 桌面
                os.path.expanduser("~/Documents"),  # 文档
                VECTOR_OUTPUT_DIR,  # 我们的输出目录
                os.getcwd(),  # 当前工作目录
            ]

            generated_files = []
            current_time = time.time()

            for location in possible_locations:
                if os.path.exists(location):
                    try:
                        for file in os.listdir(location):
                            file_path = os.path.join(location, file)
                            if os.path.isfile(file_path):
                                # 检查文件是否是最近生成的（5分钟内）
                                file_time = os.path.getmtime(file_path)
                                if current_time - file_time < 300:  # 5分钟
                                    # 检查是否是矢量图文件
                                    if file.lower().endswith(('.svg', '.png', '.eps', '.ai')):
                                        generated_files.append(file_path)
                                        logger.info(f"找到生成的文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"检查目录 {location} 时出错: {e}")

            return generated_files

        except Exception as e:
            logger.error(f"检查生成文件时出错: {e}")
            return []

    @staticmethod
    def _organize_output_files(generated_files, base_filename):
        """整理输出文件"""
        try:
            logger.info("整理输出文件")

            # 确保输出目录存在
            os.makedirs(VECTOR_OUTPUT_DIR, exist_ok=True)

            result = {
                "svg_file": None,
                "png_file": None,
                "preview_file": None
            }

            for file_path in generated_files:
                file_ext = os.path.splitext(file_path)[1].lower()

                if file_ext == '.svg':
                    # 复制SVG文件
                    new_svg_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.svg")
                    shutil.copy2(file_path, new_svg_path)
                    result["svg_file"] = os.path.basename(new_svg_path)
                    logger.info(f"SVG文件已复制到: {new_svg_path}")

                elif file_ext == '.png':
                    # 复制PNG文件
                    new_png_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.png")
                    shutil.copy2(file_path, new_png_path)
                    result["png_file"] = os.path.basename(new_png_path)
                    logger.info(f"PNG文件已复制到: {new_png_path}")

            # 生成预览图
            preview_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}_preview.png")
            try:
                pyautogui.screenshot(preview_path)
                result["preview_file"] = os.path.basename(preview_path)
            except:
                pass

            return result

        except Exception as e:
            logger.error(f"整理输出文件时出错: {e}")
            return None

# 全局实例
vectorize_service = VectorizeService()