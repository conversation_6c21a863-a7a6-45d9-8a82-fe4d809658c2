import os
import time
import logging
import tempfile
import shutil
import subprocess
import pyautogui
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入win32相关模块，采用可靠的方式
try:
    import win32api
    import win32con
    import win32gui
    HAS_WIN32 = True
except ImportError:
    logger.warning("无法导入win32模块，将使用模拟模式")
    HAS_WIN32 = False

# Vector Magic 程序路径
VECTOR_MAGIC_PATH = r"C:\Program Files (x86)\Vector Magic\VectorMagic.exe"

# 输出目录
VECTOR_OUTPUT_DIR = "vector_outputs"
os.makedirs(VECTOR_OUTPUT_DIR, exist_ok=True)

class VectorizeService:
    """矢量图生成服务类"""

    @staticmethod
    def vectorize_image(image_path, quality="medium"):
        """
        使用Vector Magic将位图转换为矢量图
        
        参数:
        - image_path: 图片文件路径
        - quality: 矢量质量，可选值: low, medium, high
        
        返回:
        - 矢量图文件路径字典，包含SVG、PNG和预览图路径
        """
        try:
            # 检查图片是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return None, "图片文件不存在"
                
            # 检查Vector Magic是否已安装
            if not os.path.exists(VECTOR_MAGIC_PATH):
                logger.error(f"Vector Magic未安装或路径不正确: {VECTOR_MAGIC_PATH}")
                return None, "Vector Magic未安装或路径不正确"
                
            # 检查win32模块是否可用
            if not HAS_WIN32:
                logger.error("win32模块不可用，无法执行矢量图转换")
                return None, "系统缺少必要的win32模块，无法执行矢量图转换"
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            base_filename = f"{timestamp}_{unique_id}"
            
            # 准备输出文件路径
            output_svg_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.svg")
            output_png_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.png")
            preview_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}_preview.png")
            
            # 启动Vector Magic
            logger.info(f"启动Vector Magic处理图片: {image_path}")
            proc = subprocess.Popen([VECTOR_MAGIC_PATH, image_path])
            
            # 等待程序启动
            time.sleep(3)
            
            # 查找Vector Magic窗口
            vector_window = None
            def find_vector_magic_window(hwnd, ctx):
                if "Vector Magic" in win32gui.GetWindowText(hwnd):
                    nonlocal vector_window
                    vector_window = hwnd
                    return False
                return True
                
            win32gui.EnumWindows(find_vector_magic_window, None)
            
            if not vector_window:
                logger.error("无法找到Vector Magic窗口")
                return None, "无法找到Vector Magic窗口"
                
            # 激活窗口
            win32gui.SetForegroundWindow(vector_window)
            time.sleep(1)
            
            # 根据质量级别选择不同的设置
            if quality == "low":
                # 选择基本模式（自动按钮）
                logger.info("选择基本模式")
                auto_button_pos = VectorizeService._find_auto_button()
                if auto_button_pos:
                    pyautogui.click(auto_button_pos[0], auto_button_pos[1])
                    time.sleep(1)
            elif quality == "high":
                # 选择高级模式（高级按钮）
                logger.info("选择高级模式")
                advanced_button_pos = VectorizeService._find_advanced_button()
                if advanced_button_pos:
                    pyautogui.click(advanced_button_pos[0], advanced_button_pos[1])
                    time.sleep(1)
            else:  # medium (default)
                # 使用红色箭头标注的按钮
                logger.info("选择中等模式")
                red_arrow_button_pos = VectorizeService._find_red_arrow_button()
                if red_arrow_button_pos:
                    pyautogui.click(red_arrow_button_pos[0], red_arrow_button_pos[1])
                    time.sleep(1)
                    
            # 等待处理完成（这里可能需要更复杂的逻辑来确定何时完成）
            logger.info("等待处理完成")
            time.sleep(10)  # 假设处理需要10秒
            
            # 截图当前结果作为预览图
            pyautogui.screenshot(preview_path)
            
            # 保存SVG和PNG文件
            # 点击文件菜单
            pyautogui.hotkey('alt', 'f')
            time.sleep(0.5)
            
            # 点击保存为SVG
            pyautogui.press('v')
            time.sleep(1)
            
            # 在保存对话框中输入文件名
            pyautogui.write(output_svg_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)
            
            # 再次点击文件菜单
            pyautogui.hotkey('alt', 'f')
            time.sleep(0.5)
            
            # 点击导出PNG
            pyautogui.press('p')
            time.sleep(1)
            
            # 在保存对话框中输入文件名
            pyautogui.write(output_png_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)
            
            # 关闭Vector Magic
            win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
            
            # 检查输出文件是否创建成功
            if not (os.path.exists(output_svg_path) and os.path.exists(output_png_path)):
                logger.error("矢量图文件未成功创建")
                return None, "矢量图文件未成功创建"
                
            # 返回文件路径
            result = {
                "svg_file": os.path.basename(output_svg_path),
                "png_file": os.path.basename(output_png_path),
                "preview_file": os.path.basename(preview_path)
            }
            
            logger.info(f"矢量图生成成功: {result}")
            return result, None
            
        except Exception as e:
            logger.exception(f"矢量图生成过程中发生错误: {str(e)}")
            return None, f"矢量图生成过程中发生错误: {str(e)}"
    
    @staticmethod
    def _find_red_arrow_button():
        """
        查找红色箭头标注的按钮位置
        在实际应用中，这里应该使用计算机视觉技术如OpenCV来查找按钮
        这里简化为硬编码位置
        
        返回:
        - (x, y) 按钮中心坐标
        """
        # 这里使用图像中红色箭头指向的按钮的位置
        # 在实际应用中应该使用图像识别找到按钮
        screen_width, screen_height = pyautogui.size()
        # 假设按钮位于屏幕右侧的位置
        x = int(screen_width * 0.85)
        y = int(screen_height * 0.4)
        return (x, y)
    
    @staticmethod
    def _find_auto_button():
        """查找自动模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.25)
        return (x, y)
    
    @staticmethod
    def _find_advanced_button():
        """查找高级模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.5)
        return (x, y)

# 全局实例
vectorize_service = VectorizeService() 