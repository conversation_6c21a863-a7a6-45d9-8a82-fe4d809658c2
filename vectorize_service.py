import os
import time
import logging
import tempfile
import shutil
import subprocess
import pyautogui
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入win32相关模块，采用可靠的方式
try:
    import win32api
    import win32con
    import win32gui
    HAS_WIN32 = True
except ImportError:
    logger.warning("无法导入win32模块，将使用模拟模式")
    HAS_WIN32 = False

# Vector Magic 程序路径配置
# 默认路径列表，按优先级排序
DEFAULT_VECTOR_MAGIC_PATHS = [
    r"C:\Program Files (x86)\Vector Magic\vmde.exe",  # 实际的可执行文件
    r"C:\Program Files\Vector Magic\vmde.exe",
    r"C:\Program Files (x86)\Vector Magic\VectorMagic.exe",  # 旧版本路径
    r"C:\Program Files\Vector Magic\VectorMagic.exe"
]

# 从配置文件或环境变量读取路径
def get_vector_magic_path():
    """获取Vector Magic可执行文件路径"""
    import os

    # 首先尝试从环境变量获取
    env_path = os.getenv('VECTOR_MAGIC_PATH')
    if env_path and os.path.exists(env_path):
        return env_path

    # 尝试从配置文件读取
    config_file = 'vector_magic_config.txt'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_path = f.read().strip()
                if config_path and os.path.exists(config_path):
                    return config_path
        except Exception as e:
            logger.warning(f"读取配置文件失败: {e}")

    # 尝试默认路径
    for path in DEFAULT_VECTOR_MAGIC_PATHS:
        if os.path.exists(path):
            logger.info(f"找到Vector Magic可执行文件: {path}")
            return path

    return None

# 获取实际的Vector Magic路径
VECTOR_MAGIC_PATH = get_vector_magic_path()

# 输出目录
VECTOR_OUTPUT_DIR = "vector_outputs"
os.makedirs(VECTOR_OUTPUT_DIR, exist_ok=True)

class VectorizeService:
    """矢量图生成服务类"""

    @staticmethod
    def set_vector_magic_path(new_path):
        """设置Vector Magic可执行文件路径（管理员功能）"""
        try:
            if not os.path.exists(new_path):
                return False, f"指定的路径不存在: {new_path}"

            if not new_path.lower().endswith(('.exe', '.app')):
                return False, "指定的文件不是可执行文件"

            # 保存到配置文件
            config_file = 'vector_magic_config.txt'
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_path)

            # 更新全局变量
            global VECTOR_MAGIC_PATH
            VECTOR_MAGIC_PATH = new_path

            logger.info(f"Vector Magic路径已更新为: {new_path}")
            return True, f"Vector Magic路径已成功设置为: {new_path}"

        except Exception as e:
            logger.error(f"设置Vector Magic路径时出错: {e}")
            return False, f"设置路径时出错: {str(e)}"

    @staticmethod
    def get_current_path():
        """获取当前Vector Magic路径"""
        return VECTOR_MAGIC_PATH

    @staticmethod
    def check_installation():
        """检查Vector Magic安装状态"""
        current_path = VectorizeService.get_current_path()
        if not current_path:
            return False, "未找到Vector Magic可执行文件，请设置正确的安装路径"

        if not os.path.exists(current_path):
            return False, f"Vector Magic路径无效: {current_path}"

        return True, f"Vector Magic已正确安装: {current_path}"

    @staticmethod
    def get_system_status():
        """获取完整的系统状态信息"""
        status = {
            "vector_magic": {
                "installed": False,
                "path": None,
                "message": ""
            },
            "dependencies": {
                "win32": HAS_WIN32,
                "pyautogui": False
            },
            "system": {
                "platform": os.name,
                "python_version": None
            }
        }

        # 检查Vector Magic
        is_installed, message = VectorizeService.check_installation()
        status["vector_magic"]["installed"] = is_installed
        status["vector_magic"]["path"] = VectorizeService.get_current_path()
        status["vector_magic"]["message"] = message

        # 检查PyAutoGUI
        try:
            import pyautogui
            status["dependencies"]["pyautogui"] = True
        except ImportError:
            status["dependencies"]["pyautogui"] = False

        # 检查Python版本
        import sys
        status["system"]["python_version"] = sys.version

        return status

    @staticmethod
    def vectorize_image(image_path, quality="medium"):
        """
        使用Vector Magic将位图转换为矢量图
        
        参数:
        - image_path: 图片文件路径
        - quality: 矢量质量，可选值: low, medium, high
        
        返回:
        - 矢量图文件路径字典，包含SVG、PNG和预览图路径
        """
        try:
            # 检查图片是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return None, "图片文件不存在"
                
            # 检查Vector Magic是否已安装
            if not VECTOR_MAGIC_PATH:
                logger.error("未找到Vector Magic可执行文件，请管理员设置正确的安装路径")
                return None, "未找到Vector Magic可执行文件，请管理员设置正确的安装路径"

            if not os.path.exists(VECTOR_MAGIC_PATH):
                logger.error(f"Vector Magic路径无效: {VECTOR_MAGIC_PATH}")
                return None, f"Vector Magic路径无效: {VECTOR_MAGIC_PATH}"
                
            # 检查win32模块是否可用
            if not HAS_WIN32:
                logger.error("win32模块不可用，无法执行矢量图转换")
                return None, "系统缺少必要的win32模块，无法执行矢量图转换"
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            base_filename = f"{timestamp}_{unique_id}"
            
            # 准备输出文件路径
            output_svg_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.svg")
            output_png_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}.png")
            preview_path = os.path.join(VECTOR_OUTPUT_DIR, f"{base_filename}_preview.png")
            
            # 启动Vector Magic
            logger.info(f"启动Vector Magic处理图片: {image_path}")
            proc = subprocess.Popen([VECTOR_MAGIC_PATH, image_path])

            # 等待程序启动并查找窗口
            vector_window = VectorizeService._wait_for_vector_magic_window()
            if not vector_window:
                logger.error("无法找到Vector Magic窗口")
                try:
                    proc.terminate()
                except:
                    pass
                return None, "无法找到Vector Magic窗口，请检查Vector Magic是否正确安装"

            # 激活窗口并等待界面加载完成
            win32gui.SetForegroundWindow(vector_window)
            time.sleep(2)

            # 等待界面完全加载
            VectorizeService._wait_for_interface_ready()

            # 执行Vector Magic工作流程
            workflow_success = VectorizeService._execute_vector_magic_workflow(quality)
            if not workflow_success:
                logger.error("Vector Magic工作流程执行失败")
                try:
                    win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
                except:
                    pass
                return None, "Vector Magic工作流程执行失败"

            # 截图当前结果作为预览图
            pyautogui.screenshot(preview_path)

            # 保存结果文件
            save_success = VectorizeService._save_results(output_svg_path, output_png_path)
            if not save_success:
                logger.warning("保存结果文件可能失败，但继续处理")

            # 关闭Vector Magic
            try:
                win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
                time.sleep(1)
            except Exception as e:
                logger.warning(f"关闭Vector Magic窗口时出错: {e}")
                try:
                    proc.terminate()
                except:
                    pass
            
            # 检查输出文件是否创建成功
            if not (os.path.exists(output_svg_path) and os.path.exists(output_png_path)):
                logger.error("矢量图文件未成功创建")
                return None, "矢量图文件未成功创建"
                
            # 返回文件路径
            result = {
                "svg_file": os.path.basename(output_svg_path),
                "png_file": os.path.basename(output_png_path),
                "preview_file": os.path.basename(preview_path)
            }
            
            logger.info(f"矢量图生成成功: {result}")
            return result, None
            
        except Exception as e:
            error_msg = f"矢量图生成过程中发生错误: {str(e)}"
            logger.exception(error_msg)

            # 尝试清理资源
            try:
                if 'proc' in locals():
                    proc.terminate()
                if 'vector_window' in locals() and vector_window:
                    win32gui.PostMessage(vector_window, win32con.WM_CLOSE, 0, 0)
            except:
                pass

            # 提供更详细的错误信息
            if "找不到指定的文件" in str(e) or "No such file" in str(e):
                error_msg = "Vector Magic可执行文件未找到，请检查安装路径配置"
            elif "Access is denied" in str(e) or "权限" in str(e):
                error_msg = "权限不足，请以管理员身份运行或检查文件权限"
            elif "win32" in str(e).lower():
                error_msg = "Win32模块错误，请确保已正确安装pywin32"

            return None, error_msg
    
    @staticmethod
    def _wait_for_vector_magic_window(timeout=30):
        """等待Vector Magic窗口出现"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                vector_window = None
                def find_vector_magic_window(hwnd, ctx):
                    try:
                        window_text = win32gui.GetWindowText(hwnd)
                        if "Vector Magic" in window_text or "vmde" in window_text.lower():
                            nonlocal vector_window
                            vector_window = hwnd
                            return False
                    except:
                        pass
                    return True

                win32gui.EnumWindows(find_vector_magic_window, None)

                if vector_window:
                    logger.info(f"找到Vector Magic窗口: {win32gui.GetWindowText(vector_window)}")
                    return vector_window

            except Exception as e:
                logger.warning(f"枚举窗口时出错: {e}")

            time.sleep(0.5)

        return None

    @staticmethod
    def _get_vector_magic_window():
        """获取当前的Vector Magic窗口句柄"""
        try:
            vector_window = None
            def find_vector_magic_window(hwnd, ctx):
                try:
                    window_text = win32gui.GetWindowText(hwnd)
                    if "Vector Magic" in window_text or "vmde" in window_text.lower():
                        nonlocal vector_window
                        vector_window = hwnd
                        return False
                except:
                    pass
                return True

            win32gui.EnumWindows(find_vector_magic_window, None)
            return vector_window

        except Exception as e:
            logger.error(f"获取Vector Magic窗口时出错: {e}")
            return None

    @staticmethod
    def _wait_for_interface_ready(timeout=10):
        """等待界面准备就绪"""
        logger.info("等待Vector Magic界面加载完成...")
        time.sleep(3)  # 基本等待时间

        # 可以在这里添加更多的界面检测逻辑
        # 例如检测特定的UI元素是否出现

    @staticmethod
    def _execute_vector_magic_workflow(quality="medium"):
        """执行Vector Magic工作流程"""
        try:
            logger.info(f"开始执行Vector Magic工作流程，质量级别: {quality}")

            # 根据质量级别执行不同的操作序列
            if quality == "low":
                return VectorizeService._execute_basic_workflow()
            elif quality == "high":
                return VectorizeService._execute_advanced_workflow()
            else:  # medium
                return VectorizeService._execute_medium_workflow()

        except Exception as e:
            logger.error(f"执行Vector Magic工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_basic_workflow():
        """执行基本工作流程"""
        try:
            logger.info("执行基本工作流程")

            # 方案1：图像识别点击
            if VectorizeService._click_button_by_image("auto_button"):
                logger.info("通过图像识别成功点击自动按钮")
            else:
                # 方案2：文本识别点击
                if VectorizeService._click_button_by_text("Auto"):
                    logger.info("通过文本识别成功点击自动按钮")
                else:
                    # 方案3：坐标点击（备用）
                    logger.info("使用坐标点击作为备用方案")
                    screen_width, screen_height = pyautogui.size()
                    auto_button_x = int(screen_width * 0.3)
                    auto_button_y = int(screen_height * 0.25)
                    pyautogui.click(auto_button_x, auto_button_y)

            time.sleep(2)

            # 等待处理完成
            VectorizeService._wait_for_processing_complete()

            return True

        except Exception as e:
            logger.error(f"执行基本工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_medium_workflow():
        """执行中等质量工作流程"""
        try:
            logger.info("执行中等质量工作流程")

            # 获取Vector Magic窗口句柄
            vector_window = VectorizeService._get_vector_magic_window()
            if not vector_window:
                logger.error("无法获取Vector Magic窗口句柄")
                return False

            # 使用智能按钮检测
            process_keywords = ["Process", "处理", "Convert", "转换", "Go", "Start"]
            if VectorizeService._smart_button_detection(vector_window, process_keywords):
                logger.info("成功点击处理按钮")
            else:
                # 备用方案：坐标点击
                logger.info("使用坐标点击作为备用方案")
                screen_width, screen_height = pyautogui.size()
                process_button_x = int(screen_width * 0.5)
                process_button_y = int(screen_height * 0.4)
                pyautogui.click(process_button_x, process_button_y)

            time.sleep(2)

            # 等待处理完成
            VectorizeService._wait_for_processing_complete()

            return True

        except Exception as e:
            logger.error(f"执行中等质量工作流程时出错: {e}")
            return False

    @staticmethod
    def _execute_advanced_workflow():
        """执行高级工作流程"""
        try:
            logger.info("执行高级工作流程")

            # 点击高级设置
            screen_width, screen_height = pyautogui.size()

            # 点击高级模式按钮
            advanced_button_x = int(screen_width * 0.3)
            advanced_button_y = int(screen_height * 0.5)

            pyautogui.click(advanced_button_x, advanced_button_y)
            time.sleep(2)

            # 等待处理完成
            VectorizeService._wait_for_processing_complete()

            return True

        except Exception as e:
            logger.error(f"执行高级工作流程时出错: {e}")
            return False

    @staticmethod
    def _wait_for_processing_complete(timeout=60):
        """等待处理完成"""
        logger.info("等待Vector Magic处理完成...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 这里可以添加检测处理完成的逻辑
            # 例如检测特定的UI元素或窗口标题变化
            time.sleep(1)

            # 简单的时间等待，实际应用中可以改进为更智能的检测
            if time.time() - start_time > 15:  # 假设15秒后处理完成
                logger.info("Vector Magic处理完成")
                return True

        logger.warning("等待处理完成超时")
        return False

    @staticmethod
    def _save_results(output_svg_path, output_png_path):
        """保存结果文件"""
        try:
            logger.info("开始保存结果文件")

            # 保存SVG文件
            logger.info("保存SVG文件")
            pyautogui.hotkey('ctrl', 's')  # 或者使用菜单
            time.sleep(1)

            # 输入文件名
            pyautogui.write(output_svg_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)

            # 保存PNG文件（如果需要）
            logger.info("导出PNG文件")
            pyautogui.hotkey('ctrl', 'e')  # 或者使用导出菜单
            time.sleep(1)

            # 输入文件名
            pyautogui.write(output_png_path)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(2)

            return True

        except Exception as e:
            logger.error(f"保存结果文件时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_image(button_name, confidence=0.8):
        """通过图像识别点击按钮"""
        try:
            # 按钮图片路径
            button_images = {
                "auto_button": "button_images/auto_button.png",
                "process_button": "button_images/process_button.png",
                "advanced_button": "button_images/advanced_button.png",
                "save_button": "button_images/save_button.png",
                "export_button": "button_images/export_button.png"
            }

            if button_name not in button_images:
                logger.warning(f"未找到按钮图片配置: {button_name}")
                return False

            image_path = button_images[button_name]

            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                logger.warning(f"按钮图片文件不存在: {image_path}")
                return False

            # 在屏幕上查找按钮
            try:
                button_location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                if button_location:
                    # 点击按钮中心
                    button_center = pyautogui.center(button_location)
                    pyautogui.click(button_center)
                    logger.info(f"成功通过图像识别点击按钮: {button_name} at {button_center}")
                    return True
                else:
                    logger.warning(f"在屏幕上未找到按钮图像: {button_name}")
                    return False
            except pyautogui.ImageNotFoundException:
                logger.warning(f"图像识别失败: {button_name}")
                return False

        except Exception as e:
            logger.error(f"图像识别点击按钮时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_text(text, region=None):
        """通过OCR文本识别点击按钮"""
        try:
            # 需要安装pytesseract和Pillow
            try:
                import pytesseract
                from PIL import Image
            except ImportError:
                logger.warning("OCR功能需要安装pytesseract和Pillow")
                return False

            # 截取屏幕
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()

            # 使用OCR识别文本
            ocr_data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)

            # 查找匹配的文本
            for i, detected_text in enumerate(ocr_data['text']):
                if text.lower() in detected_text.lower() and int(ocr_data['conf'][i]) > 50:
                    # 计算文本位置
                    x = ocr_data['left'][i] + ocr_data['width'][i] // 2
                    y = ocr_data['top'][i] + ocr_data['height'][i] // 2

                    # 如果指定了区域，需要调整坐标
                    if region:
                        x += region[0]
                        y += region[1]

                    pyautogui.click(x, y)
                    logger.info(f"成功通过文本识别点击按钮: {text} at ({x}, {y})")
                    return True

            logger.warning(f"未找到文本: {text}")
            return False

        except Exception as e:
            logger.error(f"文本识别点击按钮时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_window_control(window_handle, control_id):
        """通过Windows控件ID点击按钮"""
        try:
            # 查找子窗口控件
            def enum_child_windows(hwnd, results):
                results.append(hwnd)
                return True

            child_windows = []
            win32gui.EnumChildWindows(window_handle, enum_child_windows, child_windows)

            for child_hwnd in child_windows:
                # 获取控件ID
                try:
                    ctrl_id = win32gui.GetDlgCtrlID(child_hwnd)
                    if ctrl_id == control_id:
                        # 发送点击消息
                        win32gui.PostMessage(child_hwnd, win32con.BM_CLICK, 0, 0)
                        logger.info(f"成功通过控件ID点击按钮: {control_id}")
                        return True
                except:
                    continue

            logger.warning(f"未找到控件ID: {control_id}")
            return False

        except Exception as e:
            logger.error(f"控件点击时出错: {e}")
            return False

    @staticmethod
    def _smart_button_detection(window_handle, button_keywords):
        """智能按钮检测 - 综合多种方法"""
        try:
            logger.info(f"开始智能按钮检测，关键词: {button_keywords}")

            # 获取窗口区域
            window_rect = win32gui.GetWindowRect(window_handle)
            window_region = (window_rect[0], window_rect[1],
                           window_rect[2] - window_rect[0],
                           window_rect[3] - window_rect[1])

            # 方法1：尝试图像识别
            for keyword in button_keywords:
                if VectorizeService._click_button_by_image(f"{keyword}_button"):
                    return True

            # 方法2：尝试文本识别（在窗口区域内）
            for keyword in button_keywords:
                if VectorizeService._click_button_by_text(keyword, region=window_region):
                    return True

            # 方法3：尝试颜色识别（查找特定颜色的按钮）
            if VectorizeService._click_button_by_color(window_region, button_keywords):
                return True

            # 方法4：使用机器学习预测按钮位置
            if VectorizeService._click_button_by_ml_prediction(window_region, button_keywords):
                return True

            logger.warning("所有按钮检测方法都失败了")
            return False

        except Exception as e:
            logger.error(f"智能按钮检测时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_color(region, keywords):
        """通过颜色识别点击按钮"""
        try:
            import cv2
            import numpy as np

            # 截取指定区域
            screenshot = pyautogui.screenshot(region=region)
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # 定义常见按钮颜色范围（HSV）
            button_colors = {
                "blue": [(100, 50, 50), (130, 255, 255)],    # 蓝色按钮
                "green": [(40, 50, 50), (80, 255, 255)],     # 绿色按钮
                "red": [(0, 50, 50), (10, 255, 255)],        # 红色按钮
                "gray": [(0, 0, 50), (180, 30, 200)]         # 灰色按钮
            }

            hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)

            for color_name, (lower, upper) in button_colors.items():
                lower = np.array(lower)
                upper = np.array(upper)

                # 创建颜色掩码
                mask = cv2.inRange(hsv, lower, upper)

                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    # 过滤小的轮廓
                    area = cv2.contourArea(contour)
                    if area > 500:  # 按钮最小面积
                        # 获取边界框
                        x, y, w, h = cv2.boundingRect(contour)

                        # 检查长宽比（按钮通常是矩形）
                        aspect_ratio = w / h
                        if 0.5 <= aspect_ratio <= 4.0:
                            # 计算点击位置（相对于屏幕）
                            click_x = region[0] + x + w // 2
                            click_y = region[1] + y + h // 2

                            pyautogui.click(click_x, click_y)
                            logger.info(f"通过{color_name}颜色识别点击按钮 at ({click_x}, {click_y})")
                            return True

            return False

        except ImportError:
            logger.warning("颜色识别需要安装opencv-python")
            return False
        except Exception as e:
            logger.error(f"颜色识别点击时出错: {e}")
            return False

    @staticmethod
    def _click_button_by_ml_prediction(region, keywords):
        """使用机器学习预测按钮位置"""
        try:
            # 这里可以集成预训练的按钮检测模型
            # 例如使用YOLO或其他目标检测模型

            # 简化版本：基于历史数据的位置预测
            button_history_file = "button_positions.json"

            if os.path.exists(button_history_file):
                import json
                with open(button_history_file, 'r') as f:
                    history = json.load(f)

                for keyword in keywords:
                    if keyword in history:
                        positions = history[keyword]
                        if positions:
                            # 使用最近成功的位置
                            last_pos = positions[-1]
                            click_x = region[0] + last_pos['x']
                            click_y = region[1] + last_pos['y']

                            pyautogui.click(click_x, click_y)
                            logger.info(f"通过历史位置预测点击按钮: {keyword} at ({click_x}, {click_y})")
                            return True

            return False

        except Exception as e:
            logger.error(f"ML预测点击时出错: {e}")
            return False

    @staticmethod
    def _record_successful_click(keyword, x, y, region):
        """记录成功的点击位置"""
        try:
            import json
            button_history_file = "button_positions.json"

            # 读取现有历史
            history = {}
            if os.path.exists(button_history_file):
                with open(button_history_file, 'r') as f:
                    history = json.load(f)

            # 添加新记录
            if keyword not in history:
                history[keyword] = []

            # 转换为相对坐标
            relative_x = x - region[0]
            relative_y = y - region[1]

            history[keyword].append({
                'x': relative_x,
                'y': relative_y,
                'timestamp': time.time()
            })

            # 只保留最近10次记录
            history[keyword] = history[keyword][-10:]

            # 保存历史
            with open(button_history_file, 'w') as f:
                json.dump(history, f, indent=2)

            logger.info(f"记录成功点击位置: {keyword} at ({relative_x}, {relative_y})")

        except Exception as e:
            logger.error(f"记录点击位置时出错: {e}")

    @staticmethod
    def _find_red_arrow_button():
        """
        查找红色箭头标注的按钮位置
        在实际应用中，这里应该使用计算机视觉技术如OpenCV来查找按钮
        这里简化为硬编码位置
        
        返回:
        - (x, y) 按钮中心坐标
        """
        # 这里使用图像中红色箭头指向的按钮的位置
        # 在实际应用中应该使用图像识别找到按钮
        screen_width, screen_height = pyautogui.size()
        # 假设按钮位于屏幕右侧的位置
        x = int(screen_width * 0.85)
        y = int(screen_height * 0.4)
        return (x, y)
    
    @staticmethod
    def _find_auto_button():
        """查找自动模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.25)
        return (x, y)
    
    @staticmethod
    def _find_advanced_button():
        """查找高级模式按钮位置"""
        screen_width, screen_height = pyautogui.size()
        x = int(screen_width * 0.3)
        y = int(screen_height * 0.5)
        return (x, y)

# 全局实例
vectorize_service = VectorizeService() 