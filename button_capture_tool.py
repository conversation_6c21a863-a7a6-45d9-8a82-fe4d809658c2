#!/usr/bin/env python3
"""
Vector Magic 按钮截图工具
用于捕获Vector Magic界面中的按钮图像，供自动化识别使用
"""

import os
import time
import tkinter as tk
from tkinter import messagebox, simpledialog
import pyautogui
from PIL import Image, ImageTk
import json

class ButtonCaptureGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Vector Magic 按钮截图工具")
        self.root.geometry("400x500")
        
        self.setup_ui()
        self.button_images = {}
        self.load_existing_buttons()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(self.root, text="Vector Magic 按钮截图工具", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明
        info_text = """
使用说明：
1. 启动Vector Magic软件
2. 选择要截图的按钮类型
3. 点击"开始截图"
4. 在3秒倒计时后，用鼠标选择按钮区域
5. 按钮图像将自动保存
        """
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT)
        info_label.pack(pady=10)
        
        # 按钮类型选择
        tk.Label(self.root, text="选择按钮类型:", font=("Arial", 12)).pack(pady=5)
        
        self.button_type = tk.StringVar(value="auto_button")
        button_types = [
            ("自动处理按钮", "auto_button"),
            ("处理按钮", "process_button"),
            ("高级设置按钮", "advanced_button"),
            ("保存按钮", "save_button"),
            ("导出按钮", "export_button"),
            ("自定义", "custom")
        ]
        
        for text, value in button_types:
            tk.Radiobutton(self.root, text=text, variable=self.button_type, 
                          value=value).pack(anchor=tk.W, padx=20)
        
        # 截图按钮
        capture_btn = tk.Button(self.root, text="开始截图", command=self.start_capture,
                               bg="#4CAF50", fg="white", font=("Arial", 12))
        capture_btn.pack(pady=20)
        
        # 预览区域
        tk.Label(self.root, text="已保存的按钮:", font=("Arial", 12)).pack(pady=(20, 5))
        
        self.preview_frame = tk.Frame(self.root)
        self.preview_frame.pack(pady=10, fill=tk.BOTH, expand=True)
        
        # 底部按钮
        bottom_frame = tk.Frame(self.root)
        bottom_frame.pack(pady=10)
        
        tk.Button(bottom_frame, text="刷新预览", command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        tk.Button(bottom_frame, text="清除所有", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        tk.Button(bottom_frame, text="测试识别", command=self.test_recognition).pack(side=tk.LEFT, padx=5)
    
    def start_capture(self):
        """开始截图"""
        button_type = self.button_type.get()
        
        if button_type == "custom":
            custom_name = simpledialog.askstring("自定义按钮", "请输入按钮名称:")
            if not custom_name:
                return
            button_type = custom_name.lower().replace(" ", "_") + "_button"
        
        # 隐藏窗口
        self.root.withdraw()
        
        # 倒计时
        for i in range(3, 0, -1):
            print(f"截图倒计时: {i}")
            time.sleep(1)
        
        try:
            # 让用户选择区域
            print("请用鼠标选择按钮区域...")
            
            # 使用pyautogui的区域选择功能
            # 这里简化为让用户点击按钮中心，然后自动截取周围区域
            print("请点击按钮中心...")
            
            # 等待用户点击
            original_pos = pyautogui.position()
            print("按任意键开始监听鼠标点击...")
            input()
            
            # 获取当前鼠标位置
            click_pos = pyautogui.position()
            print(f"检测到点击位置: {click_pos}")
            
            # 截取按钮周围区域 (50x25像素)
            left = click_pos.x - 25
            top = click_pos.y - 12
            width = 50
            height = 25
            
            # 确保区域在屏幕范围内
            screen_width, screen_height = pyautogui.size()
            left = max(0, min(left, screen_width - width))
            top = max(0, min(top, screen_height - height))
            
            # 截图
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
            
            # 保存图片
            image_path = f"button_images/{button_type}.png"
            os.makedirs("button_images", exist_ok=True)
            screenshot.save(image_path)
            
            # 记录按钮信息
            self.button_images[button_type] = {
                "path": image_path,
                "position": {"x": click_pos.x, "y": click_pos.y},
                "region": {"left": left, "top": top, "width": width, "height": height},
                "timestamp": time.time()
            }
            
            self.save_button_info()
            
            print(f"按钮图像已保存: {image_path}")
            messagebox.showinfo("成功", f"按钮图像已保存: {button_type}")
            
        except Exception as e:
            print(f"截图失败: {e}")
            messagebox.showerror("错误", f"截图失败: {e}")
        
        finally:
            # 恢复窗口
            self.root.deiconify()
            self.refresh_preview()
    
    def load_existing_buttons(self):
        """加载已存在的按钮信息"""
        try:
            if os.path.exists("button_info.json"):
                with open("button_info.json", "r", encoding="utf-8") as f:
                    self.button_images = json.load(f)
        except Exception as e:
            print(f"加载按钮信息失败: {e}")
    
    def save_button_info(self):
        """保存按钮信息"""
        try:
            with open("button_info.json", "w", encoding="utf-8") as f:
                json.dump(self.button_images, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存按钮信息失败: {e}")
    
    def refresh_preview(self):
        """刷新预览"""
        # 清除现有预览
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        
        # 显示已保存的按钮
        row = 0
        col = 0
        for button_name, info in self.button_images.items():
            if os.path.exists(info["path"]):
                try:
                    # 加载图片
                    img = Image.open(info["path"])
                    img = img.resize((50, 25), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(img)
                    
                    # 创建预览框架
                    frame = tk.Frame(self.preview_frame, relief=tk.RAISED, borderwidth=1)
                    frame.grid(row=row, column=col, padx=5, pady=5)
                    
                    # 显示图片
                    img_label = tk.Label(frame, image=photo)
                    img_label.image = photo  # 保持引用
                    img_label.pack()
                    
                    # 显示名称
                    name_label = tk.Label(frame, text=button_name.replace("_", " ").title())
                    name_label.pack()
                    
                    col += 1
                    if col >= 3:
                        col = 0
                        row += 1
                        
                except Exception as e:
                    print(f"加载预览图片失败: {e}")
    
    def clear_all(self):
        """清除所有按钮"""
        if messagebox.askyesno("确认", "确定要删除所有按钮图像吗？"):
            try:
                # 删除图片文件
                for info in self.button_images.values():
                    if os.path.exists(info["path"]):
                        os.remove(info["path"])
                
                # 清除记录
                self.button_images = {}
                self.save_button_info()
                
                # 刷新预览
                self.refresh_preview()
                
                messagebox.showinfo("成功", "所有按钮图像已删除")
                
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {e}")
    
    def test_recognition(self):
        """测试按钮识别"""
        if not self.button_images:
            messagebox.showwarning("警告", "没有可测试的按钮图像")
            return
        
        try:
            # 隐藏窗口
            self.root.withdraw()
            time.sleep(1)
            
            # 测试每个按钮
            results = []
            for button_name, info in self.button_images.items():
                try:
                    location = pyautogui.locateOnScreen(info["path"], confidence=0.8)
                    if location:
                        results.append(f"✓ {button_name}: 找到")
                    else:
                        results.append(f"✗ {button_name}: 未找到")
                except:
                    results.append(f"✗ {button_name}: 识别失败")
            
            # 显示结果
            result_text = "\n".join(results)
            messagebox.showinfo("识别测试结果", result_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"测试失败: {e}")
        
        finally:
            # 恢复窗口
            self.root.deiconify()
    
    def run(self):
        """运行GUI"""
        self.refresh_preview()
        self.root.mainloop()

def main():
    """主函数"""
    print("Vector Magic 按钮截图工具")
    print("请确保Vector Magic已经启动")
    
    app = ButtonCaptureGUI()
    app.run()

if __name__ == "__main__":
    main()
