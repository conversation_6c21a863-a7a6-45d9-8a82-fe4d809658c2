/* 通用样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

.container {
    max-width: 1200px;
}

/* 管理页面样式 */
#login-card {
    max-width: 500px;
    margin: 0 auto;
}

textarea {
    font-family: monospace;
    font-size: 14px;
}

.alert {
    margin-top: 20px;
    margin-bottom: 20px;
}

#current-prompt, #new-prompt {
    min-height: 200px;
}

/* 左侧导航栏样式 */
.admin-sidebar {
    background-color: #343a40;
    min-height: calc(100vh - 60px);
    padding-top: 20px;
    position: sticky;
    top: 0;
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75);
    border-radius: 0;
    padding: 12px 15px;
    margin-bottom: 5px;
}

.admin-sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.admin-sidebar .nav-link.active {
    color: #fff;
    background-color: #007bff;
}

.admin-sidebar .nav-link i {
    width: 20px;
    margin-right: 5px;
    text-align: center;
}

.admin-content {
    padding: 20px;
}

.admin-panel-section {
    display: none;
}

.admin-panel-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 500;
}

/* 标题样式 */
h1, h3 {
    font-weight: 600;
}

h3.text-muted {
    font-size: 1.5rem;
    color: #6c757d;
}

/* 页脚样式 */
.footer {
    margin-top: 3rem;
    border-top: 1px solid #dee2e6;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 10px;
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
    }
    
    .admin-sidebar {
        min-height: auto;
        margin-bottom: 20px;
    }
} 