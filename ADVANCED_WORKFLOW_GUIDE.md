# Vector Magic 高级工作流程指南

## 概述

本指南详细说明了Vector Magic的完整自动化工作流程，实现了从启动软件到保存文件的全自动化操作。

## 工作流程步骤

### 完整流程图

```
启动Vector Magic
    ↓
等待程序完全启动 (60秒超时)
    ↓
激活窗口并等待界面加载 (15秒超时)
    ↓
点击"高级模式"按钮
    ↓
连续点击5次"下一步"按钮 (每次间隔2秒)
    ↓
等待处理完成 (180秒超时)
    ↓
执行快速保存
    ↓
检查生成的文件
    ↓
整理输出文件到指定目录
    ↓
生成预览图
    ↓
安全关闭Vector Magic
```

### 详细步骤说明

#### 1. 启动阶段 (改进)
- **启动超时**: 从30秒延长到60秒
- **窗口检测**: 智能检测Vector Magic窗口
- **多重检测**: 支持"Vector Magic"和"vmde"窗口标题

#### 2. 界面准备 (新增)
- **激活窗口**: 确保Vector Magic窗口在前台
- **等待加载**: 15秒超时等待界面完全加载
- **状态检查**: 验证窗口可见性和响应性

#### 3. 高级模式选择 (智能化)
- **多种检测方法**:
  1. 智能按钮检测
  2. 图像识别
  3. 文本识别(OCR)
  4. 坐标点击(备用)
- **关键词**: "Advanced", "高级", "Expert", "专家", "Manual", "手动"

#### 4. 连续下一步操作 (自动化)
- **点击次数**: 5次下一步
- **时间间隔**: 每次点击间隔2秒
- **多重方法**:
  1. 智能按钮检测
  2. 图像识别
  3. 文本识别
  4. 键盘快捷键(Enter, Tab+Enter)

#### 5. 处理等待 (智能化)
- **超时时间**: 180秒(3分钟)
- **状态检测**: 
  - 窗口标题变化
  - 保存按钮出现
  - 进度指示器
- **进度报告**: 每10秒报告一次状态

#### 6. 快速保存 (新功能)
- **保存触发**: 
  1. 智能检测保存按钮
  2. Ctrl+S快捷键
- **路径设置**: 自动设置到指定输出目录
- **文件命名**: `vector_YYYYMMDD_HHMMSS_随机ID.svg`

#### 7. 文件管理 (自动化)
- **文件检测**: 扫描多个可能的输出位置
- **时间过滤**: 只处理5分钟内生成的文件
- **格式支持**: SVG, PNG, EPS, AI
- **自动整理**: 复制到统一输出目录

#### 8. 安全关闭 (多重保障)
- **关闭方法**:
  1. 正常窗口关闭消息
  2. Alt+F4快捷键
  3. 进程终止
  4. taskkill命令
- **等待确认**: 每种方法后等待确认

## 配置参数

### 时间设置
```json
{
  "startup_timeout": 60,        // 启动超时(秒)
  "interface_ready_timeout": 15, // 界面准备超时(秒)
  "processing_timeout": 180,     // 处理超时(秒)
  "button_click_interval": 2,    // 按钮点击间隔(秒)
  "final_wait_time": 5,         // 最终等待时间(秒)
  "close_wait_time": 3          // 关闭等待时间(秒)
}
```

### 按钮检测
```json
{
  "image_confidence": 0.8,      // 图像识别置信度
  "ocr_confidence": 60,         // OCR识别置信度
  "color_detection_enabled": true,
  "smart_detection_enabled": true
}
```

## 使用方法

### 1. 基本使用
```python
from vectorize_service import VectorizeService

# 使用高级工作流程
result, error = VectorizeService.vectorize_image("image.png", quality="high")

if error:
    print(f"错误: {error}")
else:
    print(f"成功: {result}")
```

### 2. 通过Web API
```bash
curl -X POST "http://localhost:8001/api/vectorize" \
  -F "file=@image.png" \
  -F "quality=high"
```

### 3. 测试工作流程
```bash
python test_advanced_workflow.py
```

## 故障排除

### 常见问题

1. **启动超时**
   - 检查Vector Magic安装路径
   - 确保有足够的系统资源
   - 检查防病毒软件是否阻止

2. **按钮检测失败**
   - 运行`button_capture_tool.py`截取按钮图片
   - 检查屏幕分辨率和缩放设置
   - 使用`real_time_detection.py`调试

3. **处理超时**
   - 增加处理超时时间
   - 检查图片大小和复杂度
   - 确保系统有足够内存

4. **文件保存失败**
   - 检查输出目录权限
   - 确保磁盘空间充足
   - 检查文件路径长度

### 调试工具

1. **实时检测**: `real_time_detection.py`
2. **按钮截图**: `button_capture_tool.py`
3. **工作流程测试**: `test_advanced_workflow.py`
4. **配置测试**: `test_vector_magic.py`

## 性能优化

### 1. 系统要求
- **内存**: 至少4GB可用内存
- **CPU**: 双核以上处理器
- **磁盘**: 至少2GB可用空间

### 2. 优化建议
- 关闭不必要的后台程序
- 使用SSD硬盘提高I/O性能
- 调整Vector Magic设置以平衡质量和速度

### 3. 批量处理
- 避免同时处理多个大文件
- 使用队列系统管理任务
- 监控系统资源使用情况

## 扩展功能

### 1. 自定义工作流程
可以通过修改配置文件自定义工作流程参数：
```json
{
  "advanced_workflow": {
    "next_button_clicks": 3,    // 自定义点击次数
    "custom_steps": [           // 自定义步骤
      {"action": "click", "target": "custom_button"},
      {"action": "wait", "duration": 5}
    ]
  }
}
```

### 2. 多语言支持
系统支持多语言界面的Vector Magic：
- 中文界面关键词
- 英文界面关键词
- 可扩展其他语言

### 3. 远程控制
支持远程桌面环境下的自动化操作：
- RDP兼容性
- VNC支持
- 网络延迟补偿

## 安全注意事项

1. **权限管理**: 确保适当的文件系统权限
2. **进程隔离**: 每次操作在独立进程中运行
3. **资源清理**: 自动清理临时文件和进程
4. **错误恢复**: 完善的异常处理和恢复机制

## 更新日志

### v2.0 (当前版本)
- ✅ 实现完整的高级工作流程
- ✅ 延长启动和处理等待时间
- ✅ 添加智能按钮检测
- ✅ 实现自动文件管理
- ✅ 增强安全关闭机制

### v1.0 (原始版本)
- 基本的矢量图生成功能
- 简单的自动化流程
- 硬编码的等待时间
