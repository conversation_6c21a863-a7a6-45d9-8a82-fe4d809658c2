<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地毯图案提取系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
</head>
<body>
    <script>
        // 检查是否已登录，如果未登录则跳转到登录页面
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
            } else {
                window.location.href = '/dashboard';
            }
        }
    </script>
    <div class="container">
        <header class="my-4 text-center">
            <h1>地毯图案提取系统</h1>
            <p class="lead">正在跳转到登录页面...</p>
        </header>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">上传图片</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="upload-area">
                            <div class="upload-prompt" id="upload-prompt">
                                <i class="fa fa-cloud-upload fa-3x mb-3"></i>
                                <h4>拖放图片到此处</h4>
                                <p>或</p>
                                <label for="file-input" class="btn btn-outline-primary">选择图片</label>
                                <input id="file-input" type="file" accept="image/jpeg,image/png,image/gif" style="display: none">
                            </div>
                            <div class="preview-area" id="preview-area" style="display: none;">
                                <img id="preview-image" src="#" alt="预览图">
                                <div class="preview-controls mt-3">
                                    <button class="btn btn-sm btn-outline-danger" id="remove-btn">移除</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert alert-warning mb-2 p-2 small">
                                <i class="fa fa-info-circle"></i> 点击开始处理按钮将<strong>立即扣除3积分</strong>，无论处理结果如何
                            </div>
                            <button class="btn btn-primary w-100" id="process-btn" disabled>开始处理</button>
                        </div>

                        <div class="file-info mt-3 small text-muted">
                            <p>支持的文件类型: JPG, PNG, JPEG, GIF</p>
                            <p>最大文件大小: 10MB</p>
                        </div>
                    </div>
                </div>

                <!-- 处理状态 -->
                <div class="card shadow-sm mb-4" id="processing-card" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">处理中</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <p id="processing-message">正在提取图案，请稍候...</p>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div class="card shadow-sm mb-4" id="result-card" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">处理结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>原图</h6>
                                <div class="original-image-container">
                                    <img id="original-image" src="#" alt="原图" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>提取结果</h6>
                                <div class="result-image-container">
                                    <img id="result-image" src="#" alt="提取结果" class="img-fluid rounded">
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <a id="download-btn" href="#" class="btn btn-primary" download>
                                <i class="fa fa-download"></i> 下载结果
                            </a>
                            <button id="new-upload-btn" class="btn btn-outline-secondary ms-2">
                                <i class="fa fa-refresh"></i> 上传新图片
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="error-alert" style="display: none;">
                    <strong>错误：</strong> <span id="error-message"></span>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 地毯图案提取系统</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html> 