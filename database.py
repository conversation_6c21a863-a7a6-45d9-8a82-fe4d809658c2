import sqlite3
import os
from datetime import datetime
import logging
import threading

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Database:
    """数据库管理类，负责SQLite数据库连接和基本操作"""
    
    def __init__(self, db_path=None):
        """初始化数据库连接"""
        # 从环境变量获取数据库路径，如果未设置则使用默认路径
        self.db_path = db_path or os.environ.get("DB_PATH", "frmaster.db")
        
        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            try:
                os.makedirs(db_dir, exist_ok=True)
                logger.info(f"创建数据库目录: {db_dir}")
            except Exception as e:
                logger.error(f"创建数据库目录失败: {e}")
        
        self.connection = None
        self._local = threading.local()  # 线程本地存储
        self._initialize_db()
    
    def _initialize_db(self):
        """初始化数据库，创建表结构"""
        try:
            # 连接到数据库(如果不存在会创建)
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 创建用户表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                hashed_password TEXT NOT NULL,
                salt TEXT NOT NULL,
                role TEXT NOT NULL,
                created_at TEXT NOT NULL,
                last_login TEXT,
                is_active INTEGER NOT NULL DEFAULT 1
            )
            ''')
            
            # 创建提示词表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS prompts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                content TEXT NOT NULL,
                is_default INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            ''')
            
            # 创建用户调用记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_api_calls (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                api_type TEXT NOT NULL,
                created_at TEXT NOT NULL,
                status TEXT NOT NULL,
                prompt_id INTEGER,
                result_file TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (prompt_id) REFERENCES prompts (id)
            )
            ''')
            
            # 创建系统设置表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            ''')
            
            # 创建兑换密钥表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS redeem_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                redeem_key TEXT UNIQUE NOT NULL,
                points INTEGER NOT NULL,
                days INTEGER NOT NULL,
                description TEXT,
                used INTEGER DEFAULT 0,
                used_by TEXT,
                used_at TEXT,
                created_at TEXT NOT NULL
            )
            ''')
            
            # 提交更改
            conn.commit()
            logger.info(f"数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_connection(self):
        """获取当前线程的数据库连接，如果不存在则创建新连接"""
        # 检查当前线程是否有连接
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            try:
                self._local.connection = sqlite3.connect(self.db_path)
                # 设置行工厂为字典
                self._local.connection.row_factory = sqlite3.Row
                logger.debug(f"为线程 {threading.get_ident()} 创建新数据库连接")
            except Exception as e:
                logger.error(f"数据库连接失败: {e}")
                raise
        return self._local.connection
    
    def close_connection(self):
        """关闭当前线程的数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None
            logger.debug(f"关闭线程 {threading.get_ident()} 的数据库连接")
    
    def execute_query(self, query, params=None):
        """执行查询语句并返回结果"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor
        except Exception as e:
            logger.error(f"执行查询失败: {query}, 错误: {e}")
            raise
    
    def fetch_one(self, query, params=None):
        """执行查询并返回单条记录"""
        cursor = self.execute_query(query, params)
        return cursor.fetchone()
    
    def fetch_all(self, query, params=None):
        """执行查询并返回所有记录"""
        cursor = self.execute_query(query, params)
        return cursor.fetchall()
    
    def insert(self, table, data):
        """向指定表插入数据"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, tuple(data.values()))
            conn.commit()
            return cursor.lastrowid
        except Exception as e:
            logger.error(f"插入数据失败: {e}")
            raise
    
    def update(self, table, data, condition):
        """更新指定表中符合条件的记录"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        where_clause = ' AND '.join([f"{key} = ?" for key in condition.keys()])
        
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, tuple(data.values()) + tuple(condition.values()))
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logger.error(f"更新数据失败: {e}")
            raise
    
    def delete(self, table, condition):
        """删除指定表中符合条件的记录"""
        where_clause = ' AND '.join([f"{key} = ?" for key in condition.keys()])
        
        query = f"DELETE FROM {table} WHERE {where_clause}"
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, tuple(condition.values()))
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logger.error(f"删除数据失败: {e}")
            raise
            
    def record_api_call(self, user_id, api_type, status="success", prompt_id=None):
        """记录用户API调用"""
        try:
            now = datetime.now().isoformat()
            data = {
                "user_id": user_id,
                "api_type": api_type,
                "created_at": now,
                "status": status,
                "prompt_id": prompt_id
            }
            
            # 如果prompt_id为None，则不包含在插入数据中
            if prompt_id is None:
                del data["prompt_id"]
                
            self.insert("user_api_calls", data)
            return True
        except Exception as e:
            logger.error(f"记录API调用失败: {e}")
            return False
            
    def get_user_api_calls_count(self, user_id=None):
        """获取用户API调用次数统计"""
        try:
            if user_id:
                # 获取特定用户的调用次数
                query = """
                SELECT 
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_calls
                FROM user_api_calls
                WHERE user_id = ?
                """
                result = self.fetch_one(query, (user_id,))
            else:
                # 获取所有用户的调用次数
                query = """
                SELECT 
                    user_id,
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_calls
                FROM user_api_calls
                GROUP BY user_id
                """
                result = self.fetch_all(query)
            
            return result
        except Exception as e:
            logger.error(f"获取API调用统计失败: {e}")
            return None

    def get_system_setting(self, key):
        """获取系统设置"""
        try:
            query = "SELECT setting_value FROM system_settings WHERE setting_key = ?"
            result = self.fetch_one(query, (key,))
            if result:
                return result['setting_value']
            return None
        except Exception as e:
            logger.error(f"获取系统设置时发生错误: {str(e)}")
            return None
    
    def update_system_setting(self, key, value, description=None):
        """更新或创建系统设置"""
        try:
            now = datetime.now().isoformat()
            
            # 检查设置是否存在
            existing = self.fetch_one("SELECT id FROM system_settings WHERE setting_key = ?", (key,))
            
            if existing:
                # 更新现有设置
                if description:
                    query = """
                    UPDATE system_settings 
                    SET setting_value = ?, description = ?, updated_at = ?
                    WHERE setting_key = ?
                    """
                    params = (value, description, now, key)
                else:
                    query = """
                    UPDATE system_settings 
                    SET setting_value = ?, updated_at = ?
                    WHERE setting_key = ?
                    """
                    params = (value, now, key)
            else:
                # 创建新设置
                query = """
                INSERT INTO system_settings (setting_key, setting_value, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
                """
                params = (key, value, description or '', now, now)
            
            return self.execute_query(query, params)
        except Exception as e:
            logger.error(f"更新系统设置时发生错误: {str(e)}")
            return False
    
    def list_system_settings(self):
        """获取所有系统设置"""
        try:
            query = "SELECT setting_key, setting_value, description, created_at, updated_at FROM system_settings"
            return self.fetch_all(query)
        except Exception as e:
            logger.error(f"获取系统设置列表时发生错误: {str(e)}")
            return []

# 创建数据库实例
db = Database() 