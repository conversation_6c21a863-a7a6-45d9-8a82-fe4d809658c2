#!/usr/bin/env python3
"""
地毯图案提取Web服务客户端示例
"""

import os
import requests
import argparse
from pathlib import Path
import base64
import time
from PIL import Image
from io import BytesIO

# 服务器配置
API_URL = "http://127.0.0.1:8001"  # 更新为8001端口

def upload_image(api_url, image_path):
    """上传图片并获取处理结果"""
    print(f"正在上传图片：{image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件 {image_path} 不存在")
        return
    
    # 检查文件类型
    if not image_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
        print("不支持的文件类型，仅支持PNG、JPG、JPEG和GIF")
        return
    
    # 构建API URL
    url = f"{api_url}/api/upload"
    
    # 上传文件
    with open(image_path, 'rb') as f:
        files = {'file': (os.path.basename(image_path), f, 'image/png')}
        response = requests.post(url, files=files)
    
    # 检查响应
    if response.status_code != 200:
        print(f"错误：API返回状态码 {response.status_code}")
        print(f"响应内容：{response.text}")
        return
    
    # 解析响应
    result = response.json()
    if not result.get('success'):
        print(f"处理失败：{result.get('message')}")
        return
    
    # 显示结果
    print("图片处理成功！")
    print(f"原始文件名：{result['data']['original_filename']}")
    print(f"输出文件名：{result['data']['output_filename']}")
    print(f"下载链接：{api_url}{result['data']['download_url']}")
    
    return result['data']

def download_result(api_url, download_url, output_dir):
    """下载处理结果"""
    print("正在下载处理结果...")
    
    # 构建完整URL
    full_url = f"{api_url}{download_url}"
    
    # 发送下载请求
    response = requests.get(full_url)
    
    # 检查响应
    if response.status_code != 200:
        print(f"下载失败：状态码 {response.status_code}")
        print(f"响应内容：{response.text}")
        return False
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取文件名
    filename = download_url.split("/")[-1]
    output_path = os.path.join(output_dir, filename)
    
    # 保存文件
    with open(output_path, 'wb') as f:
        f.write(response.content)
    
    print(f"结果已保存至：{output_path}")
    return True

def update_prompt(api_url, api_key, new_prompt_file):
    """更新提示词（需要管理员权限）"""
    print("正在更新提示词...")
    
    # 检查提示词文件是否存在
    if not os.path.exists(new_prompt_file):
        print(f"错误：提示词文件 {new_prompt_file} 不存在")
        return False
    
    # 读取提示词文件
    with open(new_prompt_file, 'r', encoding='utf-8') as f:
        new_prompt = f.read()
    
    # 构建API URL
    url = f"{api_url}/api/admin/update-prompt"
    
    # 发送更新请求
    headers = {'API-Key': api_key}
    data = {'new_prompt': new_prompt}
    
    response = requests.post(url, headers=headers, json=data)
    
    # 检查响应
    if response.status_code != 200:
        print(f"更新失败：状态码 {response.status_code}")
        print(f"响应内容：{response.text}")
        return False
    
    result = response.json()
    if not result.get('success'):
        print(f"更新失败：{result.get('message')}")
        return False
    
    print("提示词更新成功！")
    return True

def get_prompt(api_url, api_key):
    """获取当前提示词（需要管理员权限）"""
    print("正在获取当前提示词...")
    
    # 构建API URL
    url = f"{api_url}/api/admin/get-prompt"
    
    # 发送请求
    headers = {'API-Key': api_key}
    response = requests.get(url, headers=headers)
    
    # 检查响应
    if response.status_code != 200:
        print(f"获取失败：状态码 {response.status_code}")
        print(f"响应内容：{response.text}")
        return False
    
    result = response.json()
    if not result.get('success'):
        print(f"获取失败：{result.get('message')}")
        return False
    
    # 显示提示词
    print("\n当前提示词：")
    print("-" * 40)
    print(result['data']['prompt'])
    print("-" * 40)
    
    return result['data']['prompt']

def process_image(image_path):
    """
    调用FRMaster API处理地毯图片
    
    参数:
        image_path: 本地图片路径
    
    返回:
        (success, result): 成功状态和结果（成功时为下载URL，失败时为错误信息）
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return False, f"文件不存在: {image_path}"
        
        # 检查文件类型
        if not image_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
            return False, "不支持的文件类型，仅支持PNG、JPG、JPEG和GIF"
        
        # 准备上传文件
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/png')}
            
            # 发送请求
            print(f"正在上传图片: {image_path}")
            response = requests.post(f"{API_URL}/api/upload", files=files)
            
            # 检查响应
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    download_url = data["data"]["download_url"]
                    full_url = f"{API_URL}{download_url}"
                    print(f"处理成功，下载URL: {full_url}")
                    return True, full_url
                else:
                    return False, f"API错误: {data['message']}"
            else:
                return False, f"HTTP错误: {response.status_code}"
    
    except Exception as e:
        return False, f"处理过程中发生错误: {str(e)}"

def download_image(download_url, output_path):
    """
    下载处理后的图片
    
    参数:
        download_url: 下载URL
        output_path: 保存路径
    
    返回:
        (success, message): 成功状态和消息
    """
    try:
        # 发送请求
        response = requests.get(download_url)
        
        # 检查响应
        if response.status_code == 200:
            content_type = response.headers.get('Content-Type', '')
            
            # 检查是否是JSON响应（可能包含base64编码的图片）
            if 'application/json' in content_type:
                try:
                    data = response.json()
                    if 'image' in data and data['image'].startswith('data:image'):
                        # 从数据URI中提取base64部分
                        base64_data = data['image'].split(',')[1]
                        image_data = base64.b64decode(base64_data)
                        with open(output_path, 'wb') as f:
                            f.write(image_data)
                        print(f"图片(base64解码)已保存到: {output_path}")
                        return True, f"图片已保存到: {output_path}"
                    elif 'image' in data:
                        # 直接是base64
                        image_data = base64.b64decode(data['image'])
                        with open(output_path, 'wb') as f:
                            f.write(image_data)
                        print(f"图片(base64解码)已保存到: {output_path}")
                        return True, f"图片已保存到: {output_path}"
                except:
                    pass  # 如果不是JSON或解析失败，回退到二进制模式
                    
            # 假设响应体可能直接是base64字符串
            try:
                image_data = base64.b64decode(response.text)
                # 检查解码后的数据是否是有效的图片
                if image_data.startswith(b'\xff\xd8') or image_data.startswith(b'\x89PNG'):
                    with open(output_path, 'wb') as f:
                        f.write(image_data)
                    print(f"图片(base64文本解码)已保存到: {output_path}")
                    return True, f"图片已保存到: {output_path}"
            except:
                pass  # 如果base64解码失败，回退到二进制模式
                
            # 直接以二进制方式保存（默认方式）
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print(f"图片已保存到: {output_path}")
            return True, f"图片已保存到: {output_path}"
        else:
            return False, f"下载失败，HTTP错误: {response.status_code}"
    
    except Exception as e:
        return False, f"下载过程中发生错误: {str(e)}"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="地毯图案提取Web服务客户端")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 上传子命令
    upload_parser = subparsers.add_parser("upload", help="上传图片并处理")
    upload_parser.add_argument("image", help="要上传的图片文件路径")
    upload_parser.add_argument("--api", default="http://localhost:8000", help="API基础URL")
    upload_parser.add_argument("--output", default="./downloads", help="下载结果保存目录")
    upload_parser.add_argument("--download", action="store_true", help="自动下载处理结果")
    
    # 更新提示词子命令
    update_parser = subparsers.add_parser("update-prompt", help="更新提示词（需要管理员权限）")
    update_parser.add_argument("prompt_file", help="新提示词文件路径")
    update_parser.add_argument("--api", default="http://localhost:8000", help="API基础URL")
    update_parser.add_argument("--key", required=True, help="管理员API密钥")
    
    # 获取提示词子命令
    get_parser = subparsers.add_parser("get-prompt", help="获取当前提示词（需要管理员权限）")
    get_parser.add_argument("--api", default="http://localhost:8000", help="API基础URL")
    get_parser.add_argument("--key", required=True, help="管理员API密钥")
    
    # 简单模式子命令
    simple_parser = subparsers.add_parser("simple", help="简单交互式模式")
    simple_parser.add_argument("--api", default="http://localhost:8000", help="API基础URL")
    
    # 解析参数
    args = parser.parse_args()
    
    # 根据子命令执行相应操作
    if args.command == "upload":
        result = upload_image(args.api, args.image)
        if result and args.download:
            download_result(args.api, result['download_url'], args.output)
    elif args.command == "update-prompt":
        update_prompt(args.api, args.key, args.prompt_file)
    elif args.command == "get-prompt":
        get_prompt(args.api, args.key)
    elif args.command == "simple":
        simple_mode(args.api)
    else:
        # 如果没有指定子命令，默认进入简单交互式模式
        simple_mode(API_URL)

def simple_mode(api_url):
    """简单交互式模式"""
    global API_URL
    API_URL = api_url
    
    print("=" * 50)
    print("地毯图案提取Web服务 - 简单交互式模式")
    print("=" * 50)
    
    # 输入图片路径
    image_path = input("请输入地毯图片路径 (按Enter使用默认示例图片): ")
    if not image_path:
        # 尝试在当前目录查找示例图片
        example_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif'))]
        if example_files:
            image_path = example_files[0]
            print(f"使用找到的示例图片: {image_path}")
        else:
            print("未找到示例图片，请输入有效的图片路径")
            return
    
    # 处理图片
    success, result = process_image(image_path)
    if success:
        # 询问是否下载
        download_choice = input("是否下载处理后的图片? (y/n): ").lower()
        if download_choice == 'y' or download_choice == 'yes':
            # 设置输出文件名
            output_dir = input("请输入保存目录 (按Enter使用当前目录): ")
            if not output_dir:
                output_dir = '.'
            
            # 确保目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件名
            filename = os.path.basename(image_path)
            name, ext = os.path.splitext(filename)
            output_filename = os.path.join(output_dir, f"{name}_processed{ext}")
            
            # 下载图片
            download_success, message = download_image(result, output_filename)
            if download_success:
                # 询问是否显示图片
                show_choice = input("是否显示处理后的图片? (y/n): ").lower()
                if show_choice == 'y' or show_choice == 'yes':
                    try:
                        img = Image.open(output_filename)
                        img.show()
                    except Exception as e:
                        print(f"无法显示图片: {str(e)}")
        else:
            print("已取消下载")
    else:
        print(f"处理失败: {result}")
        
    print("\n处理完成，感谢使用！")

if __name__ == "__main__":
    main() 