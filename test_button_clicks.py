#!/usr/bin/env python3
"""
测试Vector Magic按钮点击功能
专门测试各种按钮检测和点击方法
"""

import time
import pyautogui
import win32gui
import win32con
from vectorize_service import VectorizeService

def test_window_detection():
    """测试窗口检测功能"""
    print("=" * 50)
    print("测试窗口检测功能")
    print("=" * 50)
    
    print("请手动启动Vector Magic软件...")
    input("启动完成后按Enter继续...")
    
    print("正在检测Vector Magic窗口...")
    
    # 测试窗口检测
    window = VectorizeService._get_vector_magic_window()
    if window:
        try:
            window_title = win32gui.GetWindowText(window)
            print(f"✅ 找到窗口: {window_title} (句柄: {window})")
            
            # 测试窗口操作
            print("测试窗口激活...")
            win32gui.SetForegroundWindow(window)
            time.sleep(1)
            print("✅ 窗口已激活")
            
            return window
        except Exception as e:
            print(f"❌ 窗口操作失败: {e}")
            return None
    else:
        print("❌ 未找到Vector Magic窗口")
        return None

def test_button_detection_methods(window_handle):
    """测试各种按钮检测方法"""
    print("\n" + "=" * 50)
    print("测试按钮检测方法")
    print("=" * 50)
    
    if not window_handle:
        print("❌ 没有有效的窗口句柄")
        return
    
    # 获取窗口区域
    try:
        window_rect = win32gui.GetWindowRect(window_handle)
        window_region = (window_rect[0], window_rect[1], 
                        window_rect[2] - window_rect[0], 
                        window_rect[3] - window_rect[1])
        print(f"窗口区域: {window_region}")
    except Exception as e:
        print(f"❌ 获取窗口区域失败: {e}")
        return
    
    # 测试1: 图像识别
    print("\n1. 测试图像识别...")
    try:
        import os
        if os.path.exists("button_images"):
            button_files = [f for f in os.listdir("button_images") if f.endswith('.png')]
            if button_files:
                print(f"找到 {len(button_files)} 个按钮图片")
                for btn_file in button_files[:3]:  # 只测试前3个
                    btn_name = btn_file.replace('.png', '')
                    result = VectorizeService._click_button_by_image(btn_name)
                    print(f"  {btn_file}: {'✅ 成功' if result else '❌ 失败'}")
            else:
                print("  ❌ button_images目录为空")
        else:
            print("  ❌ button_images目录不存在")
    except Exception as e:
        print(f"  ❌ 图像识别测试失败: {e}")
    
    # 测试2: 文本识别
    print("\n2. 测试文本识别...")
    try:
        test_keywords = ["OK", "Cancel", "Next", "Save", "Auto"]
        for keyword in test_keywords:
            result = VectorizeService._click_button_by_text(keyword, region=window_region)
            print(f"  '{keyword}': {'✅ 找到' if result else '❌ 未找到'}")
            if result:
                break  # 找到一个就停止，避免误点击
    except Exception as e:
        print(f"  ❌ 文本识别测试失败: {e}")
    
    # 测试3: 颜色识别
    print("\n3. 测试颜色识别...")
    try:
        result = VectorizeService._click_button_by_color(window_region, ["button"])
        print(f"  颜色识别: {'✅ 找到按钮' if result else '❌ 未找到按钮'}")
    except Exception as e:
        print(f"  ❌ 颜色识别测试失败: {e}")

def test_smart_detection(window_handle):
    """测试智能综合检测"""
    print("\n" + "=" * 50)
    print("测试智能综合检测")
    print("=" * 50)
    
    if not window_handle:
        print("❌ 没有有效的窗口句柄")
        return
    
    # 测试不同类型的按钮
    button_tests = [
        ("高级按钮", ["Advanced", "高级", "Expert"]),
        ("下一步按钮", ["Next", "下一步", "Continue"]),
        ("保存按钮", ["Save", "保存", "Export"]),
        ("确定按钮", ["OK", "确定", "Apply"])
    ]
    
    for button_name, keywords in button_tests:
        print(f"\n测试 {button_name}...")
        try:
            result = VectorizeService._smart_button_detection(window_handle, keywords)
            print(f"  {button_name}: {'✅ 检测成功' if result else '❌ 检测失败'}")
            
            if result:
                print("  ⚠️  检测到按钮，已执行点击操作")
                time.sleep(1)  # 等待界面响应
        except Exception as e:
            print(f"  ❌ {button_name} 检测异常: {e}")

def test_keyboard_shortcuts():
    """测试键盘快捷键"""
    print("\n" + "=" * 50)
    print("测试键盘快捷键")
    print("=" * 50)
    
    shortcuts = [
        ("Enter", lambda: pyautogui.press('enter')),
        ("Tab", lambda: pyautogui.press('tab')),
        ("Ctrl+S", lambda: pyautogui.hotkey('ctrl', 's')),
        ("Alt+F4", lambda: pyautogui.hotkey('alt', 'f4')),
        ("Escape", lambda: pyautogui.press('escape'))
    ]
    
    for name, action in shortcuts:
        choice = input(f"是否测试 {name}? (y/n): ").lower().strip()
        if choice == 'y':
            print(f"执行 {name}...")
            try:
                action()
                print(f"✅ {name} 执行成功")
                time.sleep(1)
            except Exception as e:
                print(f"❌ {name} 执行失败: {e}")

def test_coordinate_clicking():
    """测试坐标点击"""
    print("\n" + "=" * 50)
    print("测试坐标点击")
    print("=" * 50)
    
    print("将在3秒后点击屏幕中心...")
    time.sleep(3)
    
    try:
        screen_width, screen_height = pyautogui.size()
        center_x = screen_width // 2
        center_y = screen_height // 2
        
        print(f"点击坐标: ({center_x}, {center_y})")
        pyautogui.click(center_x, center_y)
        print("✅ 坐标点击成功")
    except Exception as e:
        print(f"❌ 坐标点击失败: {e}")

def interactive_click_test():
    """交互式点击测试"""
    print("\n" + "=" * 50)
    print("交互式点击测试")
    print("=" * 50)
    
    print("请将鼠标移动到要测试的按钮上...")
    input("准备好后按Enter...")
    
    try:
        # 获取当前鼠标位置
        x, y = pyautogui.position()
        print(f"当前鼠标位置: ({x}, {y})")
        
        choice = input("是否在此位置点击? (y/n): ").lower().strip()
        if choice == 'y':
            pyautogui.click(x, y)
            print("✅ 点击完成")
        else:
            print("取消点击")
    except Exception as e:
        print(f"❌ 交互式点击失败: {e}")

def main():
    """主函数"""
    print("Vector Magic 按钮点击测试工具")
    print("此工具用于测试各种按钮检测和点击方法")
    
    # 设置安全措施
    pyautogui.FAILSAFE = True
    print("\n⚠️  安全提示：将鼠标移动到屏幕左上角可以中断操作")
    
    # 测试选项
    tests = [
        ("窗口检测", test_window_detection),
        ("按钮检测方法", lambda: test_button_detection_methods(window_handle)),
        ("智能综合检测", lambda: test_smart_detection(window_handle)),
        ("键盘快捷键", test_keyboard_shortcuts),
        ("坐标点击", test_coordinate_clicking),
        ("交互式点击", interactive_click_test)
    ]
    
    # 首先进行窗口检测
    print("\n首先进行窗口检测...")
    window_handle = test_window_detection()
    
    # 选择要执行的测试
    print("\n请选择要执行的测试:")
    for i, (name, _) in enumerate(tests[1:], 1):  # 跳过窗口检测
        print(f"{i}. {name}")
    print("0. 全部测试")
    
    choice = input("\n请选择 (0-5): ").strip()
    
    try:
        if choice == '0':
            # 执行所有测试
            for name, test_func in tests[1:]:
                print(f"\n开始测试: {name}")
                try:
                    test_func()
                except pyautogui.FailSafeException:
                    print("检测到安全中断，测试已停止")
                    break
                except Exception as e:
                    print(f"测试 {name} 时出错: {e}")
        elif choice.isdigit() and 1 <= int(choice) <= 5:
            # 执行选定的测试
            test_index = int(choice)
            name, test_func = tests[test_index]
            print(f"\n开始测试: {name}")
            test_func()
        else:
            print("无效选择")
    except pyautogui.FailSafeException:
        print("检测到安全中断，测试已停止")
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
