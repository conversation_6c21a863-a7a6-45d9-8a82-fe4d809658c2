#!/usr/bin/env python3
"""
自动点击功能依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✓ {package} 已安装")
        return True
    except ImportError:
        print(f"! {package} 未安装")
        return False

def main():
    print("=" * 50)
    print("Vector Magic 自动点击功能依赖安装")
    print("=" * 50)
    
    # 基础依赖
    basic_packages = [
        "pyautogui",
        "pillow",
        "opencv-python",
        "numpy"
    ]
    
    # 可选依赖
    optional_packages = [
        "pytesseract",  # OCR功能
    ]
    
    print("\n检查基础依赖...")
    missing_basic = []
    for package in basic_packages:
        if not check_package(package.replace("-", "_")):
            missing_basic.append(package)
    
    print("\n检查可选依赖...")
    missing_optional = []
    for package in optional_packages:
        if not check_package(package.replace("-", "_")):
            missing_optional.append(package)
    
    # 安装缺失的基础依赖
    if missing_basic:
        print(f"\n安装缺失的基础依赖: {missing_basic}")
        for package in missing_basic:
            install_package(package)
    
    # 询问是否安装可选依赖
    if missing_optional:
        print(f"\n发现缺失的可选依赖: {missing_optional}")
        print("可选依赖说明:")
        print("- pytesseract: 用于OCR文本识别功能")
        
        choice = input("\n是否安装可选依赖? (y/n): ").lower().strip()
        if choice == 'y':
            for package in missing_optional:
                install_package(package)
        else:
            print("跳过可选依赖安装")
    
    # 特殊说明
    print("\n" + "=" * 50)
    print("安装完成！")
    print("=" * 50)
    
    print("\n重要说明:")
    print("1. 如果安装了pytesseract，还需要安装Tesseract OCR引擎:")
    print("   - Windows: 从 https://github.com/UB-Mannheim/tesseract/wiki 下载")
    print("   - 安装后需要将tesseract.exe路径添加到系统PATH")
    
    print("\n2. 可用的工具:")
    print("   - button_capture_tool.py: 按钮截图工具")
    print("   - real_time_detection.py: 实时检测工具")
    print("   - test_vector_magic.py: 配置测试工具")
    
    print("\n3. 使用建议:")
    print("   - 首先使用按钮截图工具捕获按钮图像")
    print("   - 使用实时检测工具调试检测效果")
    print("   - 在vectorize_service.py中已集成智能检测功能")

if __name__ == "__main__":
    main()
