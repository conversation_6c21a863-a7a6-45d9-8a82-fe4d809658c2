import os
import uuid
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileHandler:
    """文件处理类，负责文件上传、验证和临时文件管理"""
    
    def __init__(self):
        self.upload_folder = config.UPLOAD_FOLDER
        self.output_folder = config.OUTPUT_FOLDER
        self.allowed_extensions = config.ALLOWED_EXTENSIONS
        self.max_file_size = config.MAX_FILE_SIZE
        
        # 确保上传和输出目录存在
        os.makedirs(self.upload_folder, exist_ok=True)
        os.makedirs(self.output_folder, exist_ok=True)
    
    def allowed_file(self, filename):
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    async def save_uploaded_file(self, file):
        """保存上传的文件并进行安全检查"""
        try:
            if not file:
                return None, "没有文件上传"
            
            # 检查文件名
            original_filename = file.filename
            if not original_filename or original_filename == '':
                return None, "无效的文件名"
            
            # 检查文件类型
            if not self.allowed_file(original_filename):
                return None, f"不支持的文件类型，允许的类型: {', '.join(self.allowed_extensions)}"
            
            # 检查文件大小 - 使用异步读取
            content = await file.read()
            if len(content) > self.max_file_size:
                return None, f"文件过大，最大允许: {self.max_file_size / 1024 / 1024}MB"
            await file.seek(0)  # 重置文件指针，使用异步方法
            
            # 获取文件扩展名，保持原始文件名
            filename = secure_filename(original_filename)
            extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
            
            # 生成上传文件路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            unique_filename = f"{timestamp}_{filename}"
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # 保存文件 - 使用异步读取
            content = await file.read()
            with open(file_path, 'wb') as f:
                f.write(content)
            
            logger.info(f"文件已上传: {file_path}")
            return file_path, None
        
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return None, f"文件上传失败: {str(e)}"
    
    def generate_output_filename(self, original_filename):
        """生成输出文件名，保留原始文件名"""
        # 从原始文件名中提取基本名称和扩展名
        name_parts = original_filename.rsplit('.', 1) if '.' in original_filename else [original_filename, 'png']
        base_name = name_parts[0]
        extension = name_parts[1] if len(name_parts) > 1 else 'png'
        
        # 生成输出文件名，保留原始文件名
        output_filename = f"{base_name}_output.{extension}"
        output_path = os.path.join(self.output_folder, output_filename)
        
        # 如果文件已存在，添加时间戳以确保唯一性
        if os.path.exists(output_path):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_filename = f"{base_name}_output_{timestamp}.{extension}"
            output_path = os.path.join(self.output_folder, output_filename)
        
        return output_path
    
    def save_output_file(self, image_data, original_filename):
        """保存处理后的图片"""
        try:
            # 生成输出文件路径
            output_path = self.generate_output_filename(original_filename)
            
            # 保存图片数据
            with open(output_path, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"输出文件已保存: {output_path}")
            return output_path, os.path.basename(output_path)
        except Exception as e:
            logger.error(f"保存输出文件失败: {str(e)}")
            return None, None
    
    def clean_old_files(self, days=1):
        """清理指定天数之前的临时文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 清理上传文件夹
            for file_path in Path(self.upload_folder).glob('*'):
                if file_path.is_file() and datetime.fromtimestamp(file_path.stat().st_mtime) < cutoff_date:
                    file_path.unlink()
                    logger.info(f"已删除旧上传文件: {file_path}")
            
            # 清理输出文件夹
            for file_path in Path(self.output_folder).glob('*'):
                if file_path.is_file() and datetime.fromtimestamp(file_path.stat().st_mtime) < cutoff_date:
                    file_path.unlink()
                    logger.info(f"已删除旧输出文件: {file_path}")
            
            return True
        except Exception as e:
            logger.error(f"清理旧文件失败: {str(e)}")
            return False

# 安全文件名处理函数
def secure_filename(filename):
    """安全处理文件名，去除不安全的字符"""
    # 移除路径分隔符等不安全字符
    filename = os.path.basename(filename)
    # 移除不可见字符
    filename = ''.join(c for c in filename if c.isprintable())
    # 如果经过处理后文件名为空，使用默认名称
    if not filename:
        filename = 'unnamed_file'
    return filename

# 初始化文件处理器实例
file_handler = FileHandler() 